import { readFileSync } from "fs";
import { join } from "path";
import { neon } from "@neondatabase/serverless";

// Load environment variables from .dev.vars
function loadEnvVars() {
  try {
    const devVars = readFileSync(".dev.vars", "utf-8");
    const vars = {};
    devVars.split("\n").forEach((line) => {
      const trimmed = line.trim();
      if (trimmed && !trimmed.startsWith("#")) {
        const [key, ...valueParts] = trimmed.split("=");
        if (key && valueParts.length > 0) {
          let value = valueParts.join("=").trim();
          if (
            (value.startsWith('"') && value.endsWith('"')) ||
            (value.startsWith("'") && value.endsWith("'"))
          ) {
            value = value.slice(1, -1);
          }
          vars[key.trim()] = value;
        }
      }
    });
    return vars;
  } catch (error) {
    console.error("Error loading .dev.vars:", error);
    return {};
  }
}

async function main() {
  const envVars = loadEnvVars();
  const databaseUrl = envVars.DATABASE_URL || process.env.DATABASE_URL;

  if (!databaseUrl) {
    console.error("DATABASE_URL not found in environment variables");
    process.exit(1);
  }

  console.log("Connecting to database...");
  const sql = neon(databaseUrl);

  try {
    // Drop existing tables if they exist (to handle type conflicts)
    console.log("Dropping existing tables if they exist...");
    await sql`DROP TABLE IF EXISTS messages CASCADE`;
    await sql`DROP TABLE IF EXISTS conversations CASCADE`;

    // Create conversations table
    console.log("Creating conversations table...");
    await sql`
      CREATE TABLE conversations (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_uuid UUID NOT NULL,
        title VARCHAR(255) NOT NULL,
        model VARCHAR(100),
        provider VARCHAR(50),
        is_archived BOOLEAN NOT NULL DEFAULT false,
        last_message_at TIMESTAMPTZ,
        created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
      )
    `;

    // Create messages table
    console.log("Creating messages table...");
    await sql`
      CREATE TABLE messages (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        conversation_id UUID NOT NULL REFERENCES conversations(id) ON DELETE CASCADE,
        role VARCHAR(20) NOT NULL,
        content TEXT NOT NULL,
        model VARCHAR(100),
        provider VARCHAR(50),
        token_count INTEGER,
        metadata JSONB,
        created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
      )
    `;

    // Create indexes
    console.log("Creating indexes...");
    await sql`CREATE INDEX conversations_user_uuid_idx ON conversations(user_uuid)`;
    await sql`CREATE INDEX conversations_last_message_at_idx ON conversations(last_message_at)`;
    await sql`CREATE INDEX conversations_created_at_idx ON conversations(created_at)`;
    await sql`CREATE INDEX conversations_is_archived_idx ON conversations(is_archived)`;
    await sql`CREATE INDEX messages_conversation_id_idx ON messages(conversation_id)`;
    await sql`CREATE INDEX messages_role_idx ON messages(role)`;
    await sql`CREATE INDEX messages_created_at_idx ON messages(created_at)`;

    // Create triggers
    console.log("Creating triggers...");
    await sql`
      CREATE OR REPLACE FUNCTION update_updated_at_column()
      RETURNS TRIGGER AS $$
      BEGIN
          NEW.updated_at = NOW();
          RETURN NEW;
      END;
      $$ language 'plpgsql'
    `;

    await sql`
      CREATE TRIGGER update_conversations_updated_at 
          BEFORE UPDATE ON conversations 
          FOR EACH ROW 
          EXECUTE FUNCTION update_updated_at_column()
    `;

    await sql`
      CREATE OR REPLACE FUNCTION update_conversation_last_message()
      RETURNS TRIGGER AS $$
      BEGIN
          UPDATE conversations 
          SET last_message_at = NEW.created_at, updated_at = NOW()
          WHERE id = NEW.conversation_id;
          RETURN NEW;
      END;
      $$ language 'plpgsql'
    `;

    await sql`
      CREATE TRIGGER update_conversation_last_message_trigger
          AFTER INSERT ON messages
          FOR EACH ROW
          EXECUTE FUNCTION update_conversation_last_message()
    `;

    console.log("✅ Chat tables created successfully!");

    // Insert a test conversation for the mock user
    console.log("Creating test conversation...");
    await sql`
      INSERT INTO conversations (user_uuid, title, provider, model)
      VALUES ('550e8400-e29b-41d4-a716-************', 'Welcome Chat', 'openai', 'gpt-4o-mini')
    `;

    console.log("✅ Test conversation created!");
  } catch (error) {
    console.error("Error creating tables:", error);
    process.exit(1);
  }
}

main();

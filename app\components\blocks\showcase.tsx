import CardGrid from "./card-grid";
import ContentSection from "./content-section";

interface ShowcaseItem {
  title: string;
  description: string;
  image: string;
  url?: string;
}

interface ShowcaseProps {
  title: string;
  items: ShowcaseItem[];
}

export default function Showcase({ title, items }: ShowcaseProps) {
  return (
    <ContentSection
      title={title}
      background="showcase"
      decorations={true}
      padding="sm"
      headerSpacing="sm"
    >
      <CardGrid items={items} columns={4} variant="showcase" animationDelay={0.15} />
    </ContentSection>
  );
}

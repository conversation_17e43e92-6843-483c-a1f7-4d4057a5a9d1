import { PaymentElement, useElements, useStripe } from "@stripe/react-stripe-js";
import type React from "react";
import { useState } from "react";
import { useNotify } from "~/lib/ui/toast";

export default function CheckoutForm() {
  const stripe = useStripe();
  const elements = useElements();
  const { success, error: notifyError } = useNotify();

  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    if (!stripe || !elements) {
      // Stripe.js has not yet loaded.
      // Make sure to disable form submission until Stripe.js has loaded.
      notifyError(
        "Payment Error",
        "Stripe.js has not loaded yet. Please wait a moment and try again."
      );
      return;
    }

    setIsLoading(true);

    // Construct the return_url. This should be the full URL.
    // For example, if your success page is at /payment-success,
    // and your app is hosted at https://example.com,
    // the return_url would be https://example.com/payment-success.
    // We'll use relative paths for now, but this needs to be absolute in production.
    const { error } = await stripe.confirmPayment({
      elements,
      confirmParams: {
        // Make sure to change this to your payment completion page
        return_url: `${window.location.origin}/payment-success`, // Adjust as needed
      },
    });

    // This point will only be reached if there is an immediate error when
    // confirming the payment. Otherwise, your customer will be redirected to
    // your `return_url`. For some payment methods like iDEAL, your customer will
    // be redirected to an intermediate site first to authorize the payment, then
    // redirected to the `return_url`.
    if (error) {
      if (error.type === "card_error" || error.type === "validation_error") {
        notifyError("Payment Failed", error.message || "An unexpected error occurred.");
      } else {
        notifyError("Payment Failed", "An unexpected error occurred.");
      }
    } else {
      // This block will typically not be reached if `return_url` is used,
      // as the user is redirected.
      success("Payment Submitted", "Redirecting to confirmation page...");
    }

    setIsLoading(false);
  };

  return (
    <form id="payment-form" onSubmit={handleSubmit}>
      <PaymentElement id="payment-element" />
      <button type="submit" disabled={isLoading || !stripe || !elements} id="submit">
        <span id="button-text">
          {isLoading ? <div className="spinner" id="spinner" /> : "Pay now"}
        </span>
      </button>
      {/* Show any error or success messages */}
    </form>
  );
}

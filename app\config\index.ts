// Centralized configuration exports
// This file provides a single entry point for all configuration

// Export specific items to avoid naming conflicts
export {
  siteConfig,
  footerLinks,
  getAnalyticsProps,
  isExternalUrl,
  type FooterLinkSection,
  type FooterLink,
  type SocialPlatform,
} from "./footer.config";

export {
  designSystem,
  colors,
  typography,
  spacing as designSpacing,
  borderRadius,
  shadows,
  animations,
  componentVariants,
  layouts,
  uxPatterns,
  utils,
  breakpoints as designBreakpoints,
} from "./design-system";

export {
  default as uiConfig,
  themeConfig,
  componentDefaults,
  animationConfig,
  breakpoints as uiBreakpoints,
  zIndex,
  spacing as uiSpacing,
} from "./ui";

export { CacheKey, Theme, type ThemeType } from "./constants";

export {
  siteConfig as seoSiteConfig,
  pageConfigs,
  getPageConfig,
  getCanonicalUrl,
  getWebsiteStructuredData,
  getOrganizationStructuredData,
} from "./seo";

export {
  getLandingPageConfig,
  type LandingPageConfig,
} from "./landing";

// Re-export commonly used configs with aliases for convenience
export { siteConfig as site } from "./footer.config";
export { designSystem as design } from "./design-system";

// Environment-specific configurations (safe for client-side)
export const env = {
  isDevelopment: typeof process !== "undefined" ? process.env.NODE_ENV === "development" : false,
  isProduction: typeof process !== "undefined" ? process.env.NODE_ENV === "production" : true,
  isTest: typeof process !== "undefined" ? process.env.NODE_ENV === "test" : false,
} as const;

// API endpoints configuration
export const apiConfig = {
  baseUrl: "/api", // Use static value for client-side
  timeout: 10000,
  retries: 3,
} as const;

// Feature flags with safe defaults for client-side
export const featureFlags = {
  enableAnalytics: false, // Default to false for client-side
  enableNewsletter: false,
  enableBlog: true, // Default to true
  enableResources: false,
  enableStatus: false,
} as const;

# AI Integration Testing Guide

This document provides a comprehensive testing guide for the AI tools integration.

## Manual Testing Checklist

### 1. Environment Setup Verification

- [ ] `.dev.vars` file created with API keys
- [ ] Development server starts without errors: `yarn dev`
- [ ] Build completes successfully: `yarn build`
- [ ] AI tools page loads: `http://localhost:5173/ai-tools`

### 2. UI Component Testing

#### AI Tools Page (`/ai-tools`)
- [ ] Page loads without errors
- [ ] Three tabs are visible: Text Generation, Stream Text, Image Generation
- [ ] Provider dropdowns show available providers
- [ ] Model dropdowns populate when provider is selected
- [ ] API key status indicators show correct status (green for available, gray for missing)
- [ ] Credit balance displays correctly

#### Text Generation Tab
- [ ] Provider selection works
- [ ] Model selection updates based on provider
- [ ] Prompt textarea accepts input
- [ ] Generate button is enabled/disabled appropriately
- [ ] Loading state shows during generation
- [ ] Results display in the result area

#### Stream Text Tab
- [ ] Similar functionality to Text Generation
- [ ] Streaming text appears in real-time
- [ ] Cursor animation shows during streaming

#### Image Generation Tab
- [ ] Provider selection (OpenAI/Replicate)
- [ ] Model selection updates based on provider
- [ ] Size selection dropdown works
- [ ] Generated images display correctly

### 3. API Endpoint Testing

#### Text Generation API (`/api/ai/generate-text`)

**Test Case 1: Valid Request**
```bash
curl -X POST http://localhost:5173/api/ai/generate-text \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "Write a hello world program in Python",
    "provider": "openai",
    "model": "gpt-4o-mini"
  }'
```

Expected Response:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "text": "print(\"Hello, World!\")",
    "reasoning": null,
    "usage": {...},
    "finishReason": "stop",
    "provider": "openai",
    "model": "gpt-4o-mini"
  }
}
```

**Test Case 2: Invalid Parameters**
```bash
curl -X POST http://localhost:5173/api/ai/generate-text \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "",
    "provider": "invalid",
    "model": "invalid"
  }'
```

Expected Response:
```json
{
  "code": -1,
  "message": "Invalid parameters provided"
}
```

#### Stream Text API (`/api/ai/stream-text`)

**Test Case: Valid Streaming Request**
```bash
curl -X POST http://localhost:5173/api/ai/stream-text \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "Explain quantum computing in simple terms",
    "provider": "deepseek",
    "model": "deepseek-chat"
  }'
```

Expected: Streaming response with text chunks

#### Image Generation API (`/api/ai/generate-image`)

**Test Case: Valid Image Request**
```bash
curl -X POST http://localhost:5173/api/ai/generate-image \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "A beautiful sunset over mountains",
    "provider": "openai",
    "model": "dall-e-3",
    "size": "1024x1024"
  }'
```

Expected Response:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "images": ["https://..."],
    "provider": "openai",
    "model": "dall-e-3",
    "parameters": {...}
  }
}
```

### 4. Error Handling Testing

#### Missing API Keys
- [ ] Appropriate error messages when API keys are missing
- [ ] UI shows provider as unavailable
- [ ] API returns meaningful error messages

#### Rate Limiting
- [ ] Rate limit errors are handled gracefully
- [ ] User-friendly error messages displayed
- [ ] Retry mechanisms work (if implemented)

#### Invalid Requests
- [ ] Empty prompts are rejected
- [ ] Invalid provider/model combinations are rejected
- [ ] Malformed JSON requests return appropriate errors

### 5. Credit System Testing

- [ ] Credits are deducted after successful AI operations
- [ ] Different operations deduct correct amounts:
  - Text Generation: 5 credits
  - Stream Text: 3 credits
  - Image Generation: 10 credits
- [ ] Insufficient credits prevent operations (if implemented)
- [ ] Credit balance updates in real-time

### 6. Performance Testing

#### Response Times
- [ ] Text generation completes within reasonable time (< 30s)
- [ ] Streaming starts within 5 seconds
- [ ] Image generation completes within reasonable time (< 60s)

#### Memory Usage
- [ ] No memory leaks during extended usage
- [ ] Streaming doesn't accumulate excessive memory

### 7. Browser Compatibility

Test in multiple browsers:
- [ ] Chrome/Chromium
- [ ] Firefox
- [ ] Safari (if on macOS)
- [ ] Edge

### 8. Mobile Responsiveness

- [ ] AI tools page works on mobile devices
- [ ] Touch interactions work properly
- [ ] Text is readable on small screens

## Automated Testing

### Unit Tests

Run the unit tests:
```bash
yarn test app/lib/__tests__/ai-providers.test.ts
yarn test app/lib/__tests__/ai-utils.test.ts
```

### Integration Tests

Create integration tests for API endpoints:

```typescript
// Example integration test
describe('AI API Integration', () => {
  it('should generate text with valid parameters', async () => {
    const response = await fetch('/api/ai/generate-text', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        prompt: 'Test prompt',
        provider: 'openai',
        model: 'gpt-4o-mini'
      })
    });
    
    expect(response.ok).toBe(true);
    const data = await response.json();
    expect(data.code).toBe(0);
    expect(data.data.text).toBeDefined();
  });
});
```

## Production Testing

### Deployment Verification

1. **Environment Variables**
   - [ ] All API keys are properly set in Cloudflare Workers
   - [ ] Environment variables are accessible in production

2. **Functionality**
   - [ ] All AI providers work in production
   - [ ] Streaming works correctly
   - [ ] Image generation works

3. **Performance**
   - [ ] Cold start times are acceptable
   - [ ] Response times meet expectations
   - [ ] No timeout issues

### Monitoring

Set up monitoring for:
- [ ] API response times
- [ ] Error rates
- [ ] Credit usage patterns
- [ ] Provider availability

## Troubleshooting Common Issues

### Build Errors
- **Missing dependencies**: Run `yarn install`
- **Type errors**: Check TypeScript configuration
- **Import errors**: Verify file paths and exports

### Runtime Errors
- **API key errors**: Check environment variables
- **Network errors**: Verify provider endpoints
- **CORS errors**: Check Cloudflare Workers configuration

### Performance Issues
- **Slow responses**: Check provider status and network
- **Memory issues**: Monitor resource usage
- **Timeout errors**: Increase timeout limits if needed

## Success Criteria

The AI integration is considered successful when:

1. ✅ All manual tests pass
2. ✅ Unit tests pass with >90% coverage
3. ✅ Integration tests pass
4. ✅ Build completes without errors
5. ✅ Production deployment works
6. ✅ Performance meets requirements
7. ✅ Error handling is robust
8. ✅ User experience is smooth

## Next Steps

After successful testing:

1. **Documentation**: Update user documentation
2. **Monitoring**: Set up production monitoring
3. **Optimization**: Implement caching and optimization
4. **Features**: Add more AI providers and features
5. **Analytics**: Track usage patterns and performance

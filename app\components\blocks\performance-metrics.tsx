import CardGrid from "./card-grid";
import ContentSection from "./content-section";

interface MetricProps {
  title: string;
  value: string;
  description: string;
  icon: string;
  trend: "up" | "down" | "stable";
  color: "green" | "blue" | "purple" | "orange";
}

const metrics: MetricProps[] = [
  {
    title: "Page Load Speed",
    value: "< 1.2s",
    description: "Lightning-fast loading times",
    icon: "⚡",
    trend: "up",
    color: "green",
  },
  {
    title: "Global CDN",
    value: "300+",
    description: "Edge locations worldwide",
    icon: "🌍",
    trend: "up",
    color: "blue",
  },
  {
    title: "Uptime SLA",
    value: "99.99%",
    description: "Enterprise-grade reliability",
    icon: "🛡️",
    trend: "stable",
    color: "purple",
  },
  {
    title: "API Response",
    value: "< 50ms",
    description: "Ultra-low latency responses",
    icon: "🚀",
    trend: "up",
    color: "orange",
  },
];

export default function PerformanceMetrics() {
  // Transform metrics to CardItem format
  const cardItems = metrics.map((metric) => ({
    title: metric.title,
    description: metric.description,
    icon: metric.icon,
    value: metric.value,
    trend: metric.trend,
    color: metric.color,
  }));

  return (
    <ContentSection
      title="Built for Performance"
      description="Experience blazing-fast performance with our globally distributed infrastructure"
      background="features"
      decorations={true}
      padding="md"
      headerSpacing="md"
    >
      {/* Performance badge */}
      <div className="text-center mb-16">
        <div className="inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-full border border-blue-500/20">
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
          <span className="text-sm font-medium text-muted-foreground">Real-time Performance</span>
        </div>
      </div>

      <CardGrid items={cardItems} columns={4} variant="metric" animationDelay={0.1} />

      {/* Trust indicators */}
      <div className="text-center space-y-8 mt-16">
        <div className="flex items-center justify-center gap-8 text-sm text-muted-foreground flex-wrap">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse" />
            <span>SOC 2 Compliant</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse delay-300" />
            <span>GDPR Ready</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-purple-500 rounded-full animate-pulse delay-600" />
            <span>ISO 27001 Certified</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-orange-500 rounded-full animate-pulse delay-900" />
            <span>24/7 Monitoring</span>
          </div>
        </div>

        <p className="text-sm text-muted-foreground">
          Trusted by 10,000+ developers and enterprises worldwide
        </p>
      </div>
    </ContentSection>
  );
}

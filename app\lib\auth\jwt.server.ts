/**
 * 极简 JWT 验证器 - 支持 Google One Tap 和 Neon Auth
 * 统一处理两种 JWT 来源的验证
 */

// 用户信息接口
export interface AuthUser {
  id: string;
  uuid: string;
  email: string;
  name: string;
  avatar?: string;
  credits?: number;
  sessionId?: string;
}

// JWT 验证结果
export interface JWTVerifyResult {
  success: boolean;
  user?: AuthUser;
  error?: string;
  jwt?: string;
}

/**
 * 统一 JWT 验证器 - 支持 Google One Tap 和 Neon Auth
 * 这是整个认证系统的核心函数
 */
export async function verifyJwt(jwt: string): Promise<JWTVerifyResult> {
  if (!jwt) {
    return { success: false, error: "No JWT provided" };
  }

  try {
    // 首先尝试作为 Neon Auth token 验证
    const neonResult = await verifyNeonAuthToken(jwt);
    if (neonResult.success) {
      return { ...neonResult, jwt };
    }

    // 如果 Neon Auth 验证失败，尝试作为 Google ID Token 验证
    const googleResult = await verifyGoogleIdToken(jwt);
    if (googleResult.success) {
      return { ...googleResult, jwt };
    }

    return { success: false, error: "Invalid JWT token" };
  } catch (error) {
    console.error("JWT verification error:", error);
    return { success: false, error: "JWT verification failed" };
  }
}

/**
 * 验证 Neon Auth Token
 */
async function verifyNeonAuthToken(jwt: string): Promise<JWTVerifyResult> {
  try {
    // 调用 Neon Auth 验证 API
    const response = await fetch("https://api.stack-auth.com/v1/verify", {
      method: "POST",
      headers: {
        Authorization: `Bearer ${jwt}`,
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      return { success: false, error: "Neon Auth verification failed" };
    }

    const data = (await response.json()) as any;

    // 转换为统一的用户格式
    const user: AuthUser = {
      id: data.user_id || data.id,
      uuid: data.user_id || data.id,
      email: data.email,
      name: data.display_name || data.name || data.email,
      avatar: data.profile_image_url || data.avatar,
      credits: data.credits || 0,
    };

    return { success: true, user };
  } catch (error) {
    return { success: false, error: "Neon Auth token verification failed" };
  }
}

/**
 * 验证 Google ID Token
 */
async function verifyGoogleIdToken(jwt: string): Promise<JWTVerifyResult> {
  try {
    // 使用 Google 的 tokeninfo 端点验证
    const response = await fetch(`https://oauth2.googleapis.com/tokeninfo?id_token=${jwt}`);

    if (!response.ok) {
      return { success: false, error: "Google token verification failed" };
    }

    const data = (await response.json()) as any;

    // 验证 token 是否过期
    const now = Math.floor(Date.now() / 1000);
    if (data.exp < now) {
      return { success: false, error: "Google token expired" };
    }

    // 转换为统一的用户格式
    const user: AuthUser = {
      id: data.sub,
      uuid: data.sub,
      email: data.email,
      name: data.name,
      avatar: data.picture,
      credits: 100, // 新用户默认积分
    };

    return { success: true, user };
  } catch (error) {
    return { success: false, error: "Google token verification failed" };
  }
}

/**
 * 从 Cookie 中提取 JWT Token
 * 支持多种 Cookie 名称：g_credential (Google), stack-access-token (Neon)
 */
export function extractJwtFromCookies(request: Request): string | null {
  const cookieHeader = request.headers.get("Cookie");
  if (!cookieHeader) return null;

  const cookies = cookieHeader.split(";").reduce(
    (acc, cookie) => {
      const [key, value] = cookie.trim().split("=");
      acc[key] = value;
      return acc;
    },
    {} as Record<string, string>
  );

  // 优先检查 Google One Tap credential
  const googleCredential = cookies["g_credential"];
  if (googleCredential) return googleCredential;

  // 然后检查 Neon Auth token
  const neonToken = cookies["stack-access-token"];
  if (neonToken) return neonToken;

  // 最后检查通用 auth token
  const authToken = cookies["auth-token"];
  if (authToken) return authToken;

  return null;
}

/**
 * 清除所有认证相关的 Cookies
 */
export function clearAuthCookies(): Headers {
  const headers = new Headers();

  // 清除 Google One Tap credential
  headers.append("Set-Cookie", "g_credential=; Max-Age=0; HttpOnly; Secure; SameSite=Lax; Path=/");

  // 清除 Neon Auth token
  headers.append(
    "Set-Cookie",
    "stack-access-token=; Max-Age=0; HttpOnly; Secure; SameSite=Lax; Path=/"
  );

  // 清除通用 auth token
  headers.append("Set-Cookie", "auth-token=; Max-Age=0; HttpOnly; Secure; SameSite=Lax; Path=/");

  return headers;
}

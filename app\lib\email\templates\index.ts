export { welcomeTemplate } from "./welcome";
export { passwordResetTemplate } from "./password-reset";
export { notificationTemplate } from "./notification";
export { paymentSuccessTemplate } from "./payment-success";
// Add other templates here as they are created

import type { EmailTemplate } from "../templating.server";

export type TemplateName = "welcome" | "passwordReset" | "notification" | "payment-success";

export const templates: Record<TemplateName, EmailTemplate> = {
  welcome: welcomeTemplate,
  passwordReset: passwordResetTemplate,
  notification: notificationTemplate,
  "payment-success": paymentSuccessTemplate,
};

export const getTemplate = (name: TemplateName): EmailTemplate | undefined => {
  return templates[name];
};

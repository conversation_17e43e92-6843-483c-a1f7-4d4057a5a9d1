import { useSearchParams } from "@remix-run/react";

export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface PaginationProps {
  pagination: PaginationInfo;
  onPageChange?: (page: number) => void;
  showInfo?: boolean;
  showPageSize?: boolean;
  pageSizeOptions?: number[];
  className?: string;
}

export function Pagination({
  pagination,
  onPageChange,
  showInfo = true,
  showPageSize = true,
  pageSizeOptions = [10, 20, 50, 100],
  className = "",
}: PaginationProps) {
  const [searchParams, setSearchParams] = useSearchParams();

  const changePage = (page: number) => {
    if (page < 1 || page > pagination.totalPages) return;

    const newParams = new URLSearchParams(searchParams);
    newParams.set("page", page.toString());
    setSearchParams(newParams);

    if (onPageChange) {
      onPageChange(page);
    }
  };

  const changePageSize = (limit: number) => {
    const newParams = new URLSearchParams(searchParams);
    newParams.set("limit", limit.toString());
    newParams.set("page", "1"); // Reset to first page
    setSearchParams(newParams);
  };

  const getVisiblePages = () => {
    const { page, totalPages } = pagination;
    const delta = 2; // Number of pages to show on each side of current page
    const range = [];
    const rangeWithDots = [];

    // Calculate range
    for (let i = Math.max(2, page - delta); i <= Math.min(totalPages - 1, page + delta); i++) {
      range.push(i);
    }

    // Add first page
    if (page - delta > 2) {
      rangeWithDots.push(1, "...");
    } else {
      rangeWithDots.push(1);
    }

    // Add middle pages
    rangeWithDots.push(...range);

    // Add last page
    if (page + delta < totalPages - 1) {
      rangeWithDots.push("...", totalPages);
    } else if (totalPages > 1) {
      rangeWithDots.push(totalPages);
    }

    return rangeWithDots;
  };

  const startItem = (pagination.page - 1) * pagination.limit + 1;
  const endItem = Math.min(pagination.page * pagination.limit, pagination.total);

  if (pagination.totalPages <= 1 && !showInfo && !showPageSize) {
    return null;
  }

  return (
    <div className={`flex items-center justify-between ${className}`}>
      {/* Info and Page Size */}
      <div className="flex items-center space-x-4">
        {showInfo && (
          <div className="text-sm text-gray-700">
            Showing {startItem} to {endItem} of {pagination.total} results
          </div>
        )}

        {showPageSize && (
          <div className="flex items-center space-x-2">
            <label htmlFor="page-size-select" className="text-sm text-gray-700">
              Show:
            </label>
            <select
              id="page-size-select"
              value={pagination.limit}
              onChange={(e) => changePageSize(parseInt(e.target.value))}
              className="px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {pageSizeOptions.map((size) => (
                <option key={size} value={size}>
                  {size}
                </option>
              ))}
            </select>
            <span className="text-sm text-gray-700">per page</span>
          </div>
        )}
      </div>

      {/* Pagination Controls */}
      {pagination.totalPages > 1 && (
        <div className="flex items-center space-x-1">
          {/* Previous Button */}
          <button
            type="button"
            onClick={() => changePage(pagination.page - 1)}
            disabled={!pagination.hasPrev}
            className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-l-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Previous
          </button>

          {/* Page Numbers */}
          <div className="flex">
            {getVisiblePages().map((pageNum, index) => {
              if (pageNum === "...") {
                return (
                  <span
                    key={`dots-${pagination.page}-${index}`}
                    className="px-3 py-2 text-sm font-medium text-gray-700 bg-white border-t border-b border-gray-300"
                  >
                    ...
                  </span>
                );
              }

              const isCurrentPage = pageNum === pagination.page;
              return (
                <button
                  type="button"
                  key={pageNum}
                  onClick={() => changePage(pageNum as number)}
                  className={`px-3 py-2 text-sm font-medium border-t border-b border-gray-300 ${
                    isCurrentPage
                      ? "bg-blue-50 text-blue-600 border-blue-500"
                      : "text-gray-700 bg-white hover:bg-gray-50"
                  }`}
                >
                  {pageNum}
                </button>
              );
            })}
          </div>

          {/* Next Button */}
          <button
            type="button"
            onClick={() => changePage(pagination.page + 1)}
            disabled={!pagination.hasNext}
            className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-r-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Next
          </button>
        </div>
      )}
    </div>
  );
}

// Compact version for mobile or small spaces
export function CompactPagination({
  pagination,
  onPageChange,
  className = "",
}: Pick<PaginationProps, "pagination" | "onPageChange" | "className">) {
  const [searchParams, setSearchParams] = useSearchParams();

  const changePage = (page: number) => {
    if (page < 1 || page > pagination.totalPages) return;

    const newParams = new URLSearchParams(searchParams);
    newParams.set("page", page.toString());
    setSearchParams(newParams);

    if (onPageChange) {
      onPageChange(page);
    }
  };

  if (pagination.totalPages <= 1) {
    return null;
  }

  return (
    <div className={`flex items-center justify-between ${className}`}>
      <div className="text-sm text-gray-700">
        Page {pagination.page} of {pagination.totalPages}
      </div>

      <div className="flex space-x-2">
        <button
          type="button"
          onClick={() => changePage(pagination.page - 1)}
          disabled={!pagination.hasPrev}
          className="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
        >
          Previous
        </button>
        <button
          type="button"
          onClick={() => changePage(pagination.page + 1)}
          disabled={!pagination.hasNext}
          className="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
        >
          Next
        </button>
      </div>
    </div>
  );
}

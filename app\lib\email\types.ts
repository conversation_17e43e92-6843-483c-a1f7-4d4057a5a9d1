export interface EmailRecipient {
  name?: string;
  email: string;
}

export interface EmailTag {
  name: string;
  value: string;
}

export interface SendEmailParams {
  to: EmailRecipient | EmailRecipient[];
  from: EmailRecipient; // Resend typically uses a default 'from' configured in the domain
  subject: string;
  text?: string;
  html?: string;
  replyTo?: EmailRecipient | string;
  cc?: EmailRecipient | EmailRecipient[];
  bcc?: EmailRecipient | EmailRecipient[];
  tags?: EmailTag[];
  headers?: Record<string, string>;
}

export type SendEmailOptions = {};

export interface EmailService {
  sendEmail: (
    params: SendEmailParams,
    options?: SendEmailOptions
  ) => Promise<{ success: boolean; messageId?: string; error?: string }>;
}

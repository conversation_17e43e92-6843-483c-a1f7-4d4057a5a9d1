/**
 * Notification Service
 * Handles in-app notifications, email notifications, and notification preferences
 */

import { and, count, desc, eq } from "drizzle-orm";
import type { Database } from "~/lib/db/db";
import { notifications, users } from "~/lib/db/schema";
import { emailService, createEmailService } from "~/lib/email/service.server";
import { getTemplate } from "~/lib/email/templates";
import type { EnvironmentContext } from "~/lib/utils/env.server";

export type NotificationType =
  | "info"
  | "success"
  | "warning"
  | "error"
  | "payment"
  | "credit"
  | "usage"
  | "system"
  | "security";

export type NotificationChannel = "in_app" | "email" | "both";

export interface CreateNotificationParams {
  accountId: string;
  userId?: string;
  title: string;
  body: string;
  type: NotificationType;
  channel: NotificationChannel;
  link?: string;
  expiresAt?: Date;
  emailTemplate?: string;
  emailVariables?: Record<string, any>;
  env?: EnvironmentContext; // Add environment context for Cloudflare Workers
}

export interface NotificationPreferences {
  emailNotifications: boolean;
  pushNotifications: boolean;
  marketingEmails: boolean;
  usageAlerts: boolean;
  securityAlerts: boolean;
  paymentNotifications: boolean;
  creditNotifications: boolean;
  systemUpdates: boolean;
}

export interface UserNotification {
  id: number;
  title: string;
  body: string;
  type: NotificationType;
  channel: NotificationChannel;
  link?: string;
  dismissed: boolean;
  readAt?: Date;
  expiresAt?: Date;
  createdAt: Date;
}

/**
 * Create a new notification
 */
export async function createNotification(
  params: CreateNotificationParams,
  db: Database
): Promise<{ success: boolean; notificationId?: number; error?: string }> {
  try {
    const {
      accountId,
      userId,
      title,
      body,
      type,
      channel,
      link,
      expiresAt,
      emailTemplate,
      emailVariables,
      env,
    } = params;

    // Create in-app notification if channel includes in_app
    let notificationId: number | undefined;
    if (channel === "in_app" || channel === "both") {
      const result = await db
        .insert(notifications)
        .values({
          accountId,
          userId,
          title,
          body,
          type,
          channel: "in_app",
          link,
          expiresAt,
        })
        .returning({ id: notifications.id });

      notificationId = result[0]?.id;
    }

    // Send email notification if channel includes email
    if (channel === "email" || channel === "both") {
      await sendEmailNotification(
        {
          accountId,
          userId,
          title,
          body,
          type,
          emailTemplate,
          emailVariables,
          env,
        },
        db
      );
    }

    return { success: true, notificationId };
  } catch (error) {
    console.error("Error creating notification:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to create notification",
    };
  }
}

/**
 * Send email notification
 */
async function sendEmailNotification(
  params: {
    accountId: string;
    userId?: string;
    title: string;
    body: string;
    type: NotificationType;
    emailTemplate?: string;
    emailVariables?: Record<string, any>;
    env?: EnvironmentContext;
  },
  db: Database
): Promise<void> {
  try {
    // Get user email
    const userQuery = params.userId
      ? eq(users.id, params.userId)
      : eq(users.uuid, params.accountId); // Assuming accountId maps to user UUID

    const user = await db
      .select({ email: users.email, name: users.name })
      .from(users)
      .where(userQuery)
      .limit(1);

    if (!user || user.length === 0) {
      console.error("User not found for email notification");
      return;
    }

    const { email, name } = user[0];

    // Use custom template if provided, otherwise use default notification template
    const templateName = params.emailTemplate || "notification";
    const template = getTemplate(templateName);

    if (!template) {
      console.error(`Email template not found: ${templateName}`);
      return;
    }

    const templateVariables = {
      name,
      title: params.title,
      body: params.body,
      type: params.type,
      ...params.emailVariables,
    };

    // Create email service with environment context
    const emailServiceWithEnv = createEmailService(params.env);

    const result = await emailServiceWithEnv.sendEmail({
      to: { email, name },
      from: { email: "<EMAIL>", name: "Your App" }, // TODO: Configure from env
      subject: template.subject(templateVariables),
      html: template.html(templateVariables),
      text: template.text(templateVariables),
      tags: [
        { name: "NotificationType", value: params.type },
        { name: "EmailType", value: "Notification" },
      ],
    });

    if (!result.success) {
      console.error("Failed to send email notification:", result.error);
    }
  } catch (error) {
    console.error("Error sending email notification:", error);
  }
}

/**
 * Get user notifications with pagination
 */
export async function getUserNotifications(
  accountId: string,
  options: {
    page?: number;
    limit?: number;
    unreadOnly?: boolean;
    type?: NotificationType;
  } = {},
  db: Database
): Promise<{
  notifications: UserNotification[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalCount: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}> {
  try {
    const { page = 1, limit = 20, unreadOnly = false, type } = options;
    const offset = (page - 1) * limit;

    // Build where conditions
    let whereConditions = eq(notifications.accountId, accountId);

    if (unreadOnly) {
      whereConditions = and(whereConditions, eq(notifications.readAt, null));
    }

    if (type) {
      whereConditions = and(whereConditions, eq(notifications.type, type));
    }

    // Get total count
    const totalCountResult = await db
      .select({ count: count() })
      .from(notifications)
      .where(whereConditions);

    const totalCount = totalCountResult[0]?.count || 0;
    const totalPages = Math.ceil(totalCount / limit);

    // Get notifications
    const notificationResults = await db
      .select()
      .from(notifications)
      .where(whereConditions)
      .orderBy(desc(notifications.createdAt))
      .limit(limit)
      .offset(offset);

    const userNotifications: UserNotification[] = notificationResults.map((n) => ({
      id: n.id,
      title: n.title,
      body: n.body,
      type: n.type as NotificationType,
      channel: n.channel as NotificationChannel,
      link: n.link,
      dismissed: n.dismissed,
      readAt: n.readAt,
      expiresAt: n.expiresAt,
      createdAt: n.createdAt,
    }));

    return {
      notifications: userNotifications,
      pagination: {
        currentPage: page,
        totalPages,
        totalCount,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    };
  } catch (error) {
    console.error("Error getting user notifications:", error);
    return {
      notifications: [],
      pagination: {
        currentPage: 1,
        totalPages: 0,
        totalCount: 0,
        hasNext: false,
        hasPrev: false,
      },
    };
  }
}

/**
 * Mark notification as read
 */
export async function markNotificationAsRead(
  notificationId: number,
  accountId: string,
  db: Database
): Promise<{ success: boolean; error?: string }> {
  try {
    await db
      .update(notifications)
      .set({ readAt: new Date() })
      .where(and(eq(notifications.id, notificationId), eq(notifications.accountId, accountId)));

    return { success: true };
  } catch (error) {
    console.error("Error marking notification as read:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to mark notification as read",
    };
  }
}

/**
 * Mark notification as dismissed
 */
export async function dismissNotification(
  notificationId: number,
  accountId: string,
  db: Database
): Promise<{ success: boolean; error?: string }> {
  try {
    await db
      .update(notifications)
      .set({ dismissed: true })
      .where(and(eq(notifications.id, notificationId), eq(notifications.accountId, accountId)));

    return { success: true };
  } catch (error) {
    console.error("Error dismissing notification:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to dismiss notification",
    };
  }
}

/**
 * Mark all notifications as read
 */
export async function markAllNotificationsAsRead(
  accountId: string,
  db: Database
): Promise<{ success: boolean; error?: string }> {
  try {
    await db
      .update(notifications)
      .set({ readAt: new Date() })
      .where(and(eq(notifications.accountId, accountId), eq(notifications.readAt, null)));

    return { success: true };
  } catch (error) {
    console.error("Error marking all notifications as read:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to mark all notifications as read",
    };
  }
}

/**
 * Get unread notification count
 */
export async function getUnreadNotificationCount(accountId: string, db: Database): Promise<number> {
  try {
    const result = await db
      .select({ count: count() })
      .from(notifications)
      .where(
        and(
          eq(notifications.accountId, accountId),
          eq(notifications.readAt, null),
          eq(notifications.dismissed, false)
        )
      );

    return result[0]?.count || 0;
  } catch (error) {
    console.error("Error getting unread notification count:", error);
    return 0;
  }
}

/**
 * Clean up expired notifications
 */
export async function cleanupExpiredNotifications(db: Database): Promise<void> {
  try {
    const now = new Date();
    await db
      .update(notifications)
      .set({ dismissed: true })
      .where(
        and(
          eq(notifications.dismissed, false)
          // notifications.expiresAt < now (need to implement proper date comparison)
        )
      );
  } catch (error) {
    console.error("Error cleaning up expired notifications:", error);
  }
}

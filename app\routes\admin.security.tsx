/**
 * Admin Security Dashboard
 * Comprehensive security monitoring and management interface
 */

import type { LoaderFunctionArgs } from "@remix-run/cloudflare";
import { json } from "@remix-run/cloudflare";
import { useLoaderData } from "@remix-run/react";
import {
  Activity,
  AlertCircle,
  AlertTriangle,
  Ban,
  CheckCircle,
  Clock,
  Download,
  Eye,
  Globe,
  Lock,
  RefreshCw,
  Server,
  Settings,
  Shield,
  TrendingUp,
  Users,
  XCircle,
  Zap,
} from "lucide-react";
import { useState } from "react";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { getAuditEvents, getSecurityMetrics } from "~/lib/security/audit-logger.server";
import { getAllRateLimitEntries } from "~/lib/security/rate-limiter.server";

export async function loader({ request, context }: LoaderFunctionArgs) {
  try {
    // TODO: Add admin authentication check

    // Get security metrics
    const securityMetrics = getSecurityMetrics();

    // Get recent audit events
    const recentEvents = getAuditEvents({ limit: 50 });

    // Get rate limit status
    const rateLimitEntries = getAllRateLimitEntries();
    const rateLimitStats = {
      totalKeys: rateLimitEntries.size,
      activeKeys: Array.from(rateLimitEntries.values()).filter((entry) => entry.count > 0).length,
      blockedRequests: Array.from(rateLimitEntries.values()).reduce(
        (sum, entry) => sum + entry.count,
        0
      ),
    };

    return json({
      success: true,
      data: {
        securityMetrics,
        recentEvents,
        rateLimitStats,
        systemStatus: {
          rateLimiting: true,
          inputValidation: true,
          auditLogging: true,
          threatDetection: true,
          securityHeaders: true,
        },
      },
    });
  } catch (error) {
    console.error("Error loading security dashboard:", error);
    return json({ success: false, error: "Failed to load security dashboard" }, { status: 500 });
  }
}

export default function AdminSecurityPage() {
  const { data } = useLoaderData<typeof loader>();
  const [selectedTab, setSelectedTab] = useState("overview");

  const { securityMetrics, recentEvents, rateLimitStats, systemStatus } = data;

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case "CRITICAL":
        return "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400";
      case "HIGH":
        return "bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400";
      case "MEDIUM":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400";
      case "LOW":
        return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
    }
  };

  const getEventTypeIcon = (eventType: string) => {
    switch (eventType) {
      case "AUTHENTICATION":
        return <Lock className="w-4 h-4" />;
      case "AUTHORIZATION":
        return <Shield className="w-4 h-4" />;
      case "SECURITY_VIOLATION":
        return <AlertTriangle className="w-4 h-4" />;
      case "RATE_LIMIT":
        return <Zap className="w-4 h-4" />;
      case "API_ACCESS":
        return <Globe className="w-4 h-4" />;
      case "SYSTEM_EVENT":
        return <Server className="w-4 h-4" />;
      default:
        return <Activity className="w-4 h-4" />;
    }
  };

  const getStatusIcon = (status: boolean) => {
    return status ? (
      <CheckCircle className="w-5 h-5 text-green-500" />
    ) : (
      <XCircle className="w-5 h-5 text-red-500" />
    );
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                Security Dashboard
              </h1>
              <p className="mt-2 text-gray-600 dark:text-gray-400">
                Monitor and manage system security, threats, and access controls
              </p>
            </div>
            <div className="flex items-center space-x-3">
              <Button variant="outline" size="sm">
                <Download className="w-4 h-4 mr-2" />
                Export Logs
              </Button>
              <Button variant="outline" size="sm">
                <RefreshCw className="w-4 h-4 mr-2" />
                Refresh
              </Button>
              <Button variant="outline" size="sm">
                <Settings className="w-4 h-4 mr-2" />
                Settings
              </Button>
            </div>
          </div>
        </div>

        {/* Security Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Shield className="w-8 h-8 text-blue-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Total Events
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {securityMetrics.totalEvents.toLocaleString()}
                  </p>
                  <p className="text-xs text-blue-600">
                    {securityMetrics.lastHourEvents} in last hour
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <AlertTriangle className="w-8 h-8 text-red-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Security Violations
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {securityMetrics.securityViolations}
                  </p>
                  <p className="text-xs text-red-600">
                    {securityMetrics.suspiciousActivity} suspicious
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Lock className="w-8 h-8 text-yellow-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Failed Logins
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {securityMetrics.failedLogins}
                  </p>
                  <p className="text-xs text-yellow-600">Authentication failures</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Zap className="w-8 h-8 text-purple-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Rate Limited
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {securityMetrics.rateLimitExceeded}
                  </p>
                  <p className="text-xs text-purple-600">Blocked requests</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Detailed Security Information */}
        <Tabs value={selectedTab} onValueChange={setSelectedTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="events">Audit Events</TabsTrigger>
            <TabsTrigger value="threats">Threats</TabsTrigger>
            <TabsTrigger value="rate-limits">Rate Limits</TabsTrigger>
            <TabsTrigger value="system">System Status</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Recent Critical Events */}
              <Card>
                <CardHeader>
                  <CardTitle>Recent Critical Events</CardTitle>
                  <CardDescription>Latest high-priority security events</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {securityMetrics.recentCriticalEvents.slice(0, 5).map((event, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-3 border rounded-lg"
                      >
                        <div className="flex items-center space-x-3">
                          {getEventTypeIcon(event.eventType)}
                          <div>
                            <p className="font-medium text-sm">{event.action}</p>
                            <p className="text-xs text-gray-500">
                              {event.ipAddress} • {new Date(event.timestamp).toLocaleString()}
                            </p>
                          </div>
                        </div>
                        <Badge className={getSeverityColor(event.severity)}>{event.severity}</Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Top Risky IPs */}
              <Card>
                <CardHeader>
                  <CardTitle>Top Risky IP Addresses</CardTitle>
                  <CardDescription>IP addresses with highest risk scores</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {securityMetrics.topRiskyIPs.slice(0, 5).map((ip, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <Globe className="w-4 h-4 text-gray-500" />
                          <div>
                            <p className="font-medium text-sm">{ip.ip}</p>
                            <p className="text-xs text-gray-500">{ip.eventCount} events</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className="text-sm font-medium">Risk: {ip.riskScore}</span>
                          <Button variant="outline" size="sm">
                            <Ban className="w-3 h-3" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="events" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Recent Audit Events</CardTitle>
                <CardDescription>Detailed security event log</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {recentEvents.slice(0, 20).map((event, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-4 border rounded-lg"
                    >
                      <div className="flex items-center space-x-4">
                        {getEventTypeIcon(event.eventType)}
                        <div>
                          <div className="flex items-center space-x-2">
                            <p className="font-medium">{event.action}</p>
                            <Badge className={getSeverityColor(event.severity)}>
                              {event.severity}
                            </Badge>
                          </div>
                          <div className="flex items-center space-x-4 text-sm text-gray-500 mt-1">
                            <span>{event.ipAddress}</span>
                            <span>
                              {event.method} {event.endpoint}
                            </span>
                            <span>{new Date(event.timestamp).toLocaleString()}</span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        {event.success ? (
                          <CheckCircle className="w-5 h-5 text-green-500" />
                        ) : (
                          <XCircle className="w-5 h-5 text-red-500" />
                        )}
                        <Button variant="outline" size="sm">
                          <Eye className="w-3 h-3" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="threats" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Threat Detection</CardTitle>
                  <CardDescription>Active threat monitoring status</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span>SQL Injection Detection</span>
                      {getStatusIcon(true)}
                    </div>
                    <div className="flex items-center justify-between">
                      <span>XSS Protection</span>
                      {getStatusIcon(true)}
                    </div>
                    <div className="flex items-center justify-between">
                      <span>Path Traversal Detection</span>
                      {getStatusIcon(true)}
                    </div>
                    <div className="flex items-center justify-between">
                      <span>Command Injection Detection</span>
                      {getStatusIcon(true)}
                    </div>
                    <div className="flex items-center justify-between">
                      <span>Suspicious Pattern Detection</span>
                      {getStatusIcon(true)}
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Security Violations</CardTitle>
                  <CardDescription>Recent security threat attempts</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {recentEvents
                      .filter((event) => event.eventType === "SECURITY_VIOLATION")
                      .slice(0, 5)
                      .map((event, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between p-3 border rounded-lg"
                        >
                          <div>
                            <p className="font-medium text-sm">{event.action}</p>
                            <p className="text-xs text-gray-500">
                              {event.ipAddress} • {new Date(event.timestamp).toLocaleString()}
                            </p>
                          </div>
                          <Badge className={getSeverityColor(event.severity)}>
                            {event.severity}
                          </Badge>
                        </div>
                      ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="rate-limits" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <Activity className="w-8 h-8 text-blue-500" />
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                        Total Keys
                      </p>
                      <p className="text-2xl font-bold text-gray-900 dark:text-white">
                        {rateLimitStats.totalKeys}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <TrendingUp className="w-8 h-8 text-green-500" />
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                        Active Keys
                      </p>
                      <p className="text-2xl font-bold text-gray-900 dark:text-white">
                        {rateLimitStats.activeKeys}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <Ban className="w-8 h-8 text-red-500" />
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                        Blocked Requests
                      </p>
                      <p className="text-2xl font-bold text-gray-900 dark:text-white">
                        {rateLimitStats.blockedRequests}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="system" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Security System Status</CardTitle>
                <CardDescription>Current status of security components</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span>Rate Limiting</span>
                      {getStatusIcon(systemStatus.rateLimiting)}
                    </div>
                    <div className="flex items-center justify-between">
                      <span>Input Validation</span>
                      {getStatusIcon(systemStatus.inputValidation)}
                    </div>
                    <div className="flex items-center justify-between">
                      <span>Audit Logging</span>
                      {getStatusIcon(systemStatus.auditLogging)}
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span>Threat Detection</span>
                      {getStatusIcon(systemStatus.threatDetection)}
                    </div>
                    <div className="flex items-center justify-between">
                      <span>Security Headers</span>
                      {getStatusIcon(systemStatus.securityHeaders)}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}

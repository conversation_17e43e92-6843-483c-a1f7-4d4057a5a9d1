/**
 * Feedback model and database operations
 */

import { eq } from "drizzle-orm";
import type { Database } from "~/lib/db/db";
import { feedback } from "~/lib/db/schema";

export interface Feedback {
  id?: number;
  user_uuid?: string;
  content: string;
  rating?: number;
  status: "created" | "reviewed" | "resolved";
  created_at: string;
  updated_at?: string;
}

/**
 * Insert new feedback
 */
export async function insertFeedback(feedbackData: Feedback, db: Database): Promise<Feedback> {
  try {
    const now = new Date();
    const result = await db
      .insert(feedback)
      .values({
        userUuid: feedbackData.user_uuid || null,
        content: feedbackData.content,
        rating: feedbackData.rating || null,
        status: feedbackData.status,
        createdAt: now,
        updatedAt: now,
      })
      .returning();

    const newFeedback = result[0];
    return {
      id: newFeedback.id,
      user_uuid: newFeedback.userUuid || undefined,
      content: newFeedback.content,
      rating: newFeedback.rating || undefined,
      status: newFeedback.status as "created" | "reviewed" | "resolved",
      created_at: newFeedback.createdAt.toISOString(),
      updated_at: newFeedback.updatedAt?.toISOString(),
    };
  } catch (error) {
    console.error("Error inserting feedback:", error);
    throw error;
  }
}

/**
 * Get feedback by user UUID
 */
export async function getFeedbackByUser(userUuid: string, db: Database): Promise<Feedback[]> {
  try {
    const result = await db.select().from(feedback).where(eq(feedback.userUuid, userUuid));

    return result.map((item) => ({
      id: item.id,
      user_uuid: item.userUuid || undefined,
      content: item.content,
      rating: item.rating || undefined,
      status: item.status as "created" | "reviewed" | "resolved",
      created_at: item.createdAt.toISOString(),
      updated_at: item.updatedAt?.toISOString(),
    }));
  } catch (error) {
    console.error("Error getting feedback by user:", error);
    return [];
  }
}

/**
 * Update feedback status
 */
export async function updateFeedbackStatus(
  id: number,
  status: Feedback["status"],
  db: Database
): Promise<boolean> {
  try {
    await db
      .update(feedback)
      .set({
        status: status,
        updatedAt: new Date(),
      })
      .where(eq(feedback.id, id));

    return true;
  } catch (error) {
    console.error("Error updating feedback status:", error);
    return false;
  }
}

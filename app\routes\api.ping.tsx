import type { ActionFunctionArgs } from "@remix-run/cloudflare";
import { respData, respErr } from "~/lib/api/resp";
import { CreditsAmount, CreditsTransType, decreaseCredits } from "~/services/credit";
import { getUserUuid } from "~/services/user";

export async function action({ request, context }: ActionFunctionArgs) {
  if (request.method !== "POST") {
    return respErr("Method not allowed");
  }

  try {
    const db = context.db;
    if (!db) {
      return respErr("Database not available");
    }

    const body = (await request.json()) as { message?: string };
    const { message } = body;

    if (!message) {
      return respErr("invalid params");
    }

    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("no auth");
    }

    // decrease credits for ping
    await decreaseCredits(
      {
        user_uuid,
        trans_type: CreditsTransType.Ping,
        credits: CreditsAmount.PingCost,
      },
      db
    );

    return respData({
      pong: `received message: ${message}`,
    });
  } catch (e) {
    console.log("ping failed:", e);
    return respErr("ping failed");
  }
}

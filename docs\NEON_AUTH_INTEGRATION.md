# Neon Auth Integration (Experimental)

This document describes the experimental Neon Auth integration for the Remix + Cloudflare Workers project.

## Overview

Neon Auth is an experimental authentication system that provides built-in authentication with user data synced directly to your Neon PostgreSQL database. This integration allows you to use Neon Auth as an alternative to the existing Google One Tap authentication.

## ⚠️ Important Notes

- **Experimental Feature**: This integration is experimental and may have compatibility issues with Cloudflare Workers
- **No Official Support**: Neon Auth doesn't officially support Remix, so this is a custom adaptation
- **React Router Patterns**: We've adapted React Router patterns to work with Remix routing
- **Potential Issues**: There may be SSR/hydration issues and bundle size concerns

## Setup Instructions

### 1. Get Neon Auth Credentials

1. Go to [https://console.neon.tech/app/projects](https://console.neon.tech/app/projects)
2. Select your project and go to the "Auth" page
3. Click "Setup instructions" and then "Set up Auth"
4. Copy the environment variables provided

### 2. Configure Environment Variables

Add these variables to your `.dev.vars` file:

```bash
# Enable Neon Auth (set to "true" to use Neon Auth instead of Google One Tap)
NEON_AUTH_ENABLED="true"

# Neon Auth Configuration
VITE_STACK_PROJECT_ID="your-neon-auth-project-id"
VITE_STACK_PUBLISHABLE_CLIENT_KEY="your-neon-auth-publishable-key"
STACK_SECRET_SERVER_KEY="your-neon-auth-secret-key"
```

### 3. Feature Flag System

The integration uses a feature flag system:

- When `NEON_AUTH_ENABLED="true"`: Uses Neon Auth
- When `NEON_AUTH_ENABLED="false"` or unset: Uses Google One Tap (default)

## Architecture

### Files Created/Modified

#### New Files:
- `app/lib/auth/neon-auth.client.ts` - Client-side Neon Auth configuration
- `app/lib/auth/neon-auth.server.ts` - Server-side Neon Auth utilities
- `app/lib/auth/auth-config.ts` - Feature flag configuration
- `app/lib/auth/unified-middleware.server.ts` - Unified auth middleware
- `app/components/auth/neon-auth-provider.tsx` - Remix-compatible provider
- `app/routes/neon-auth.tsx` - Neon Auth layout route
- `app/routes/neon-auth.$.tsx` - Neon Auth handler routes
- `app/routes/auth.neon-login.tsx` - Neon Auth login page

#### Modified Files:
- `app/root.tsx` - Added Neon Auth environment variables
- `app/routes/auth.login.tsx` - Added Neon Auth option
- `.dev.vars.example` - Added Neon Auth configuration

### Authentication Flow

1. **Feature Detection**: System checks `NEON_AUTH_ENABLED` flag
2. **Route Selection**: 
   - Neon Auth enabled: `/auth/neon-login` and `/neon-auth/*` routes
   - Google One Tap: `/auth/login` route
3. **Unified Middleware**: `unified-middleware.server.ts` handles both auth systems
4. **Client Integration**: Environment variables injected into window object

## Usage

### Accessing Authentication Routes

#### Neon Auth Routes:
- `/auth/neon-login` - Neon Auth login page
- `/neon-auth/sign-in` - Neon Auth sign-in flow
- `/neon-auth/sign-up` - Neon Auth sign-up flow
- `/neon-auth/sign-out` - Neon Auth sign-out

#### Google One Tap Routes (fallback):
- `/auth/login` - Google One Tap login page

### Using Unified Authentication

```typescript
import { requireUnifiedAuth, optionalUnifiedAuth } from "~/lib/auth/unified-middleware.server";

// In your route loaders
export async function loader({ request, context }: LoaderFunctionArgs) {
  // Require authentication (redirects if not authenticated)
  const user = await requireUnifiedAuth(request, context.cloudflare?.env);
  
  // Or optional authentication
  const user = await optionalUnifiedAuth(request, context.cloudflare?.env);
  
  return json({ user });
}
```

## Known Limitations

1. **Cloudflare Workers Compatibility**: Some Neon Auth features may not work in Cloudflare Workers environment
2. **SSR Issues**: React components may have hydration mismatches
3. **Bundle Size**: Additional dependencies increase bundle size
4. **Token Validation**: Server-side token validation is not fully implemented
5. **Error Handling**: Limited error handling for edge cases

## Troubleshooting

### Common Issues

1. **"Neon Auth not configured" error**
   - Check that all environment variables are set correctly
   - Ensure `NEON_AUTH_ENABLED="true"`

2. **Client-side errors**
   - Check browser console for JavaScript errors
   - Verify environment variables are injected into window object

3. **Authentication not working**
   - Verify Neon Auth credentials are correct
   - Check network requests in browser dev tools

### Debugging

Enable debug logging by checking browser console for:
- `Neon Auth:` prefixed messages
- Environment variable injection logs
- Authentication flow logs

## Migration Guide

### From Google One Tap to Neon Auth

1. Set `NEON_AUTH_ENABLED="true"` in your environment
2. Configure Neon Auth credentials
3. Test authentication flows
4. Update any hardcoded auth URLs in your application

### Rollback to Google One Tap

1. Set `NEON_AUTH_ENABLED="false"` or remove the variable
2. Ensure Google One Tap credentials are configured
3. Clear any Neon Auth cookies/tokens

## Future Improvements

- [ ] Complete server-side token validation implementation
- [ ] Add proper error boundaries and fallbacks
- [ ] Improve Cloudflare Workers compatibility
- [ ] Add comprehensive testing
- [ ] Optimize bundle size
- [ ] Add user migration utilities

## Support

Since this is an experimental integration:
- Check the browser console for error messages
- Review the implementation in the created files
- Consider falling back to Google One Tap if issues occur
- Report issues with detailed error logs and environment details

import { <PERSON> } from "@remix-run/react";
import { <PERSON><PERSON><PERSON>, Check, Menu } from "lucide-react";
import { type ReactNode, useState } from "react";
import { Badge } from "~/components/ui/badge";
import { <PERSON><PERSON> } from "~/components/ui/button";
import { Sheet, Sheet<PERSON>ontent, SheetTrigger } from "~/components/ui/sheet";
import { designSystem } from "~/config/design-system";
import { cn } from "~/lib/utils/utils";
import Footer from "./footer";
import Header, { type HeaderProps } from "./header";
import Sidebar, { type SidebarProps } from "./sidebar";

// Hero section interface
interface HeroSection {
  badge?: {
    text: string;
    variant?: "default" | "success" | "warning" | "destructive";
  };
  title: string;
  subtitle?: string;
  description?: string;
  buttons?: {
    text: string;
    href: string;
    variant?: "primary" | "secondary" | "outline";
    icon?: ReactNode;
  }[];
  trustIndicators?: string[];
  backgroundPattern?: "gradient" | "dots" | "grid" | "none";
}

// Unified layout props
export interface UnifiedLayoutProps {
  children: ReactNode;

  // Layout options
  showHeader?: boolean;
  showFooter?: boolean;
  showSidebar?: boolean;
  className?: string;
  containerSize?: "default" | "small" | "full";

  // Header configuration
  headerProps?: HeaderProps;

  // Sidebar configuration
  sidebarProps?: Partial<SidebarProps>;
  sidebarCollapsible?: boolean;
  customSidebar?: ReactNode; // Custom sidebar component

  // Hero section
  hero?: HeroSection;
}

export default function UnifiedLayout({
  children,
  showHeader = true,
  showFooter = true,
  showSidebar = false,
  className = "",
  containerSize = "default",
  headerProps = {},
  sidebarProps = {},
  sidebarCollapsible = true,
  customSidebar,
  hero,
}: UnifiedLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  const closeSidebar = () => setSidebarOpen(false);

  // Container class based on size
  const containerClass =
    containerSize === "small"
      ? designSystem.layouts.containerSmall
      : containerSize === "full"
        ? "w-full px-4"
        : designSystem.layouts.container;

  // Background pattern helper
  const getBackgroundPattern = (pattern?: string) => {
    switch (pattern) {
      case "gradient":
        return "bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-blue-950/20 dark:via-background dark:to-purple-950/20";
      case "dots":
        return "bg-background bg-[radial-gradient(circle_at_1px_1px,_hsl(var(--muted))_1px,_transparent_0)] bg-[size:20px_20px]";
      case "grid":
        return "bg-background bg-[linear-gradient(to_right,_hsl(var(--border))_1px,_transparent_1px),_linear-gradient(to_bottom,_hsl(var(--border))_1px,_transparent_1px)] bg-[size:20px_20px]";
      default:
        return "bg-background";
    }
  };

  // Button variant helper
  const getButtonVariant = (variant?: string) => {
    switch (variant) {
      case "primary":
        return "default";
      case "secondary":
        return "secondary";
      case "outline":
        return "outline";
      default:
        return "default";
    }
  };

  return (
    <div className={cn("min-h-screen flex flex-col", className)}>
      {/* Header */}
      {showHeader && <Header {...headerProps} />}

      <div className="flex flex-1">
        {/* Desktop Sidebar */}
        {showSidebar && (
          <aside
            className={cn(
              "hidden lg:flex lg:flex-col lg:fixed lg:inset-y-0 lg:z-40 lg:w-64 transition-all duration-300",
              showHeader ? "lg:top-16" : "lg:top-0",
              sidebarCollapsed ? "lg:w-16" : "lg:w-64"
            )}
          >
            {customSidebar || (
              <Sidebar {...sidebarProps} className={cn("h-full", sidebarProps.className)} />
            )}
          </aside>
        )}

        {/* Mobile Sidebar */}
        {showSidebar && (
          <Sheet open={sidebarOpen} onOpenChange={setSidebarOpen}>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon" className="fixed top-4 left-4 z-50 lg:hidden">
                <Menu className="h-5 w-5" />
                <span className="sr-only">Toggle sidebar</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="w-80 p-0">
              {customSidebar || (
                <Sidebar
                  {...sidebarProps}
                  onClose={closeSidebar}
                  showCloseButton={true}
                  className="h-full"
                />
              )}
            </SheetContent>
          </Sheet>
        )}

        {/* Main Content Area */}
        <main
          className={cn(
            "flex-1 flex flex-col",
            showSidebar ? "lg:ml-64" : "",
            showHeader ? (showSidebar ? "lg:pt-0" : "pt-16") : ""
          )}
        >
          {/* Hero Section */}
          {hero && (
            <section
              className={`relative overflow-hidden ${getBackgroundPattern(hero.backgroundPattern)}`}
            >
              {/* Background decorative elements */}
              <div className="absolute inset-0 overflow-hidden">
                <div className="absolute -top-40 -right-32 w-96 h-96 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full blur-3xl animate-pulse" />
                <div className="absolute -bottom-40 -left-32 w-96 h-96 bg-gradient-to-tr from-purple-400/20 to-pink-400/20 rounded-full blur-3xl animate-pulse delay-1000" />
              </div>

              <div className={`relative z-10 ${containerClass} py-24 lg:py-32`}>
                <div className="text-center max-w-4xl mx-auto">
                  {/* Badge */}
                  {hero.badge && (
                    <div className="mb-8 animate-fade-in">
                      <Badge
                        variant={hero.badge.variant || "default"}
                        className="px-4 py-2 text-sm font-medium bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border border-gray-200/50 dark:border-gray-700/50 shadow-lg"
                      >
                        {hero.badge.text}
                      </Badge>
                    </div>
                  )}

                  {/* Title */}
                  <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 dark:text-white mb-6 animate-slide-up">
                    <span className="bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 dark:from-white dark:via-blue-200 dark:to-purple-200 bg-clip-text text-transparent">
                      {hero.title}
                    </span>
                  </h1>

                  {/* Subtitle */}
                  {hero.subtitle && (
                    <h2 className="text-xl md:text-2xl font-semibold text-gray-700 dark:text-gray-300 mb-4 animate-slide-up delay-100">
                      {hero.subtitle}
                    </h2>
                  )}

                  {/* Description */}
                  {hero.description && (
                    <p className="text-lg text-gray-600 dark:text-gray-400 mb-8 max-w-3xl mx-auto leading-relaxed animate-slide-up delay-200">
                      {hero.description}
                    </p>
                  )}

                  {/* Buttons */}
                  {hero.buttons && hero.buttons.length > 0 && (
                    <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8 animate-slide-up delay-300">
                      {hero.buttons.map((button, index) => (
                        <div
                          key={`${button.text || button.label}-${index}`}
                          className={index === 0 ? "group relative" : ""}
                        >
                          {index === 0 && (
                            <div className="absolute -inset-1 bg-gradient-to-r from-blue-600 via-purple-600 to-cyan-600 rounded-2xl blur opacity-30 group-hover:opacity-50 transition duration-1000 group-hover:duration-200" />
                          )}
                          <Button
                            asChild
                            size="lg"
                            variant={getButtonVariant(button.variant)}
                            className={`
                              relative px-8 py-4 text-lg font-semibold rounded-2xl transition-all duration-300 hover:scale-105 hover:shadow-xl
                              ${
                                index === 0
                                  ? "bg-gradient-to-r from-blue-600 via-purple-600 to-cyan-600 hover:from-blue-700 hover:via-purple-700 hover:to-cyan-700 text-white border-0 shadow-2xl hover:shadow-blue-500/40"
                                  : "border-2 border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 shadow-xl"
                              }
                            `}
                          >
                            <Link to={button.href} className="flex items-center gap-3">
                              {button.text}
                              {button.icon ||
                                (index === 0 && (
                                  <ArrowRight className="h-5 w-5 transition-transform group-hover:translate-x-1" />
                                ))}
                            </Link>
                          </Button>
                        </div>
                      ))}
                    </div>
                  )}

                  {/* Trust Indicators */}
                  {hero.trustIndicators && hero.trustIndicators.length > 0 && (
                    <div className="flex flex-wrap justify-center gap-6 text-sm text-gray-600 dark:text-gray-400 animate-fade-in delay-500">
                      {hero.trustIndicators.map((indicator, index) => (
                        <div
                          key={`${indicator.text || indicator.label}-${index}`}
                          className="flex items-center gap-2 bg-white/50 dark:bg-gray-900/50 px-4 py-2 rounded-full backdrop-blur-sm"
                        >
                          <Check className="h-4 w-4 text-green-500" />
                          <span>{indicator}</span>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </section>
          )}

          {/* Page Content */}
          <div
            className={cn(
              "flex-1",
              showSidebar ? "p-4 lg:p-8" : "",
              hero ? "" : containerSize === "full" ? "" : containerClass
            )}
          >
            {hero ? (
              <div className={containerSize === "full" ? "" : containerClass}>{children}</div>
            ) : (
              children
            )}
          </div>

          {/* Footer */}
          {showFooter && <Footer />}
        </main>
      </div>
    </div>
  );
}

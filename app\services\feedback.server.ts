/**
 * Enhanced Feedback Service
 * Handles feedback creation, management, and admin operations
 */

import { and, count, desc, eq, like, or, sql } from "drizzle-orm";
import type { Database } from "~/lib/db/db";
import { feedback, users } from "~/lib/db/schema";
import type { Feedback } from "~/lib/db/schema";

export type FeedbackType =
  | "bug_report"
  | "feature_request"
  | "general_feedback"
  | "support_request"
  | "complaint"
  | "compliment"
  | "suggestion";
export type FeedbackPriority = "low" | "medium" | "high" | "urgent";
export type FeedbackStatus = "open" | "in_progress" | "resolved" | "closed" | "duplicate";

export interface CreateFeedbackParams {
  userUuid?: string;
  userEmail?: string;
  userName?: string;
  title: string;
  content: string;
  type?: FeedbackType;
  priority?: FeedbackPriority;
  rating?: number;
  tags?: string[];
  metadata?: Record<string, any>;
}

export interface UpdateFeedbackParams {
  title?: string;
  content?: string;
  type?: FeedbackType;
  priority?: FeedbackPriority;
  status?: FeedbackStatus;
  rating?: number;
  tags?: string[];
  adminNotes?: string;
  assignedTo?: string;
}

export interface FeedbackWithUser extends Feedback {
  user?: {
    name: string;
    email: string;
    avatar?: string;
  };
  tagsArray?: string[];
  metadataObject?: Record<string, any>;
}

export interface GetFeedbackOptions {
  page?: number;
  limit?: number;
  status?: FeedbackStatus;
  type?: FeedbackType;
  priority?: FeedbackPriority;
  assignedTo?: string;
  search?: string;
  userUuid?: string;
}

/**
 * Create new feedback
 */
export async function createFeedback(
  params: CreateFeedbackParams,
  db: Database
): Promise<{ success: boolean; feedback?: Feedback; error?: string }> {
  try {
    const {
      userUuid,
      userEmail,
      userName,
      title,
      content,
      type = "general_feedback",
      priority = "medium",
      rating,
      tags,
      metadata,
    } = params;

    // Validate required fields
    if (!title || title.trim().length < 3) {
      return { success: false, error: "Title must be at least 3 characters long" };
    }

    if (!content || content.trim().length < 10) {
      return { success: false, error: "Content must be at least 10 characters long" };
    }

    if (title.length > 200) {
      return { success: false, error: "Title must be less than 200 characters" };
    }

    if (content.length > 5000) {
      return { success: false, error: "Content must be less than 5000 characters" };
    }

    if (rating && (rating < 1 || rating > 5)) {
      return { success: false, error: "Rating must be between 1 and 5" };
    }

    // For anonymous feedback, require email
    if (!userUuid && !userEmail) {
      return { success: false, error: "Email is required for anonymous feedback" };
    }

    const result = await db
      .insert(feedback)
      .values({
        userUuid,
        userEmail,
        userName,
        title: title.trim(),
        content: content.trim(),
        type,
        priority,
        status: "open",
        rating,
        tags: tags ? JSON.stringify(tags) : null,
        metadata: metadata ? JSON.stringify(metadata) : null,
      })
      .returning();

    if (result.length === 0) {
      return { success: false, error: "Failed to create feedback" };
    }

    return { success: true, feedback: result[0] };
  } catch (error) {
    console.error("Error creating feedback:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to create feedback",
    };
  }
}

/**
 * Get feedback with pagination and filtering
 */
export async function getFeedback(
  options: GetFeedbackOptions = {},
  db: Database
): Promise<{
  feedback: FeedbackWithUser[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalCount: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}> {
  try {
    const { page = 1, limit = 20, status, type, priority, assignedTo, search, userUuid } = options;

    const offset = (page - 1) * limit;

    // Build where conditions
    let whereConditions: any = undefined;

    if (status) {
      whereConditions = and(whereConditions, eq(feedback.status, status));
    }

    if (type) {
      whereConditions = and(whereConditions, eq(feedback.type, type));
    }

    if (priority) {
      whereConditions = and(whereConditions, eq(feedback.priority, priority));
    }

    if (assignedTo) {
      whereConditions = and(whereConditions, eq(feedback.assignedTo, assignedTo));
    }

    if (userUuid) {
      whereConditions = and(whereConditions, eq(feedback.userUuid, userUuid));
    }

    if (search) {
      const searchCondition = or(
        like(feedback.title, `%${search}%`),
        like(feedback.content, `%${search}%`),
        like(feedback.userEmail, `%${search}%`),
        like(feedback.userName, `%${search}%`)
      );
      whereConditions = and(whereConditions, searchCondition);
    }

    // Get total count
    const totalCountResult = await db
      .select({ count: count() })
      .from(feedback)
      .where(whereConditions);

    const totalCount = totalCountResult[0]?.count || 0;
    const totalPages = Math.ceil(totalCount / limit);

    // Get feedback with user information
    const feedbackResults = await db
      .select({
        feedback: feedback,
        user: {
          name: users.name,
          email: users.email,
          avatar: users.avatar,
        },
      })
      .from(feedback)
      .leftJoin(users, eq(feedback.userUuid, users.uuid))
      .where(whereConditions)
      .orderBy(desc(feedback.createdAt))
      .limit(limit)
      .offset(offset);

    // Format results
    const formattedFeedback: FeedbackWithUser[] = feedbackResults.map((result) => ({
      ...result.feedback,
      user: result.user.name ? result.user : undefined,
      tagsArray: result.feedback.tags ? JSON.parse(result.feedback.tags) : [],
      metadataObject: result.feedback.metadata ? JSON.parse(result.feedback.metadata) : {},
    }));

    return {
      feedback: formattedFeedback,
      pagination: {
        currentPage: page,
        totalPages,
        totalCount,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    };
  } catch (error) {
    console.error("Error getting feedback:", error);
    return {
      feedback: [],
      pagination: {
        currentPage: 1,
        totalPages: 0,
        totalCount: 0,
        hasNext: false,
        hasPrev: false,
      },
    };
  }
}

/**
 * Get feedback by ID
 */
export async function getFeedbackById(
  feedbackId: number,
  db: Database
): Promise<FeedbackWithUser | null> {
  try {
    const result = await db
      .select({
        feedback: feedback,
        user: {
          name: users.name,
          email: users.email,
          avatar: users.avatar,
        },
      })
      .from(feedback)
      .leftJoin(users, eq(feedback.userUuid, users.uuid))
      .where(eq(feedback.id, feedbackId))
      .limit(1);

    if (result.length === 0) {
      return null;
    }

    const feedbackData = result[0];
    return {
      ...feedbackData.feedback,
      user: feedbackData.user.name ? feedbackData.user : undefined,
      tagsArray: feedbackData.feedback.tags ? JSON.parse(feedbackData.feedback.tags) : [],
      metadataObject: feedbackData.feedback.metadata
        ? JSON.parse(feedbackData.feedback.metadata)
        : {},
    };
  } catch (error) {
    console.error("Error getting feedback by ID:", error);
    return null;
  }
}

/**
 * Update feedback
 */
export async function updateFeedback(
  feedbackId: number,
  params: UpdateFeedbackParams,
  db: Database
): Promise<{ success: boolean; error?: string }> {
  try {
    const updateData: any = {
      updatedAt: new Date(),
    };

    if (params.title !== undefined) {
      if (params.title.trim().length < 3) {
        return { success: false, error: "Title must be at least 3 characters long" };
      }
      if (params.title.length > 200) {
        return { success: false, error: "Title must be less than 200 characters" };
      }
      updateData.title = params.title.trim();
    }

    if (params.content !== undefined) {
      if (params.content.trim().length < 10) {
        return { success: false, error: "Content must be at least 10 characters long" };
      }
      if (params.content.length > 5000) {
        return { success: false, error: "Content must be less than 5000 characters" };
      }
      updateData.content = params.content.trim();
    }

    if (params.type !== undefined) {
      updateData.type = params.type;
    }

    if (params.priority !== undefined) {
      updateData.priority = params.priority;
    }

    if (params.status !== undefined) {
      updateData.status = params.status;
      if (params.status === "resolved" || params.status === "closed") {
        updateData.resolvedAt = new Date();
      }
    }

    if (params.rating !== undefined) {
      if (params.rating < 1 || params.rating > 5) {
        return { success: false, error: "Rating must be between 1 and 5" };
      }
      updateData.rating = params.rating;
    }

    if (params.tags !== undefined) {
      updateData.tags = JSON.stringify(params.tags);
    }

    if (params.adminNotes !== undefined) {
      updateData.adminNotes = params.adminNotes;
    }

    if (params.assignedTo !== undefined) {
      updateData.assignedTo = params.assignedTo;
    }

    const result = await db
      .update(feedback)
      .set(updateData)
      .where(eq(feedback.id, feedbackId))
      .returning();

    if (result.length === 0) {
      return { success: false, error: "Feedback not found" };
    }

    return { success: true };
  } catch (error) {
    console.error("Error updating feedback:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to update feedback",
    };
  }
}

/**
 * Delete feedback
 */
export async function deleteFeedback(
  feedbackId: number,
  db: Database
): Promise<{ success: boolean; error?: string }> {
  try {
    const result = await db.delete(feedback).where(eq(feedback.id, feedbackId)).returning();

    if (result.length === 0) {
      return { success: false, error: "Feedback not found" };
    }

    return { success: true };
  } catch (error) {
    console.error("Error deleting feedback:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to delete feedback",
    };
  }
}

/**
 * Get feedback statistics
 */
export async function getFeedbackStats(db: Database): Promise<{
  total: number;
  byStatus: Record<FeedbackStatus, number>;
  byType: Record<FeedbackType, number>;
  byPriority: Record<FeedbackPriority, number>;
  averageRating: number;
}> {
  try {
    // Get total count
    const totalResult = await db.select({ count: count() }).from(feedback);
    const total = totalResult[0]?.count || 0;

    // Get counts by status
    const statusResults = await db
      .select({
        status: feedback.status,
        count: count(),
      })
      .from(feedback)
      .groupBy(feedback.status);

    const byStatus: Record<FeedbackStatus, number> = {
      open: 0,
      in_progress: 0,
      resolved: 0,
      closed: 0,
      duplicate: 0,
    };

    statusResults.forEach((result) => {
      byStatus[result.status as FeedbackStatus] = result.count;
    });

    // Get counts by type
    const typeResults = await db
      .select({
        type: feedback.type,
        count: count(),
      })
      .from(feedback)
      .groupBy(feedback.type);

    const byType: Record<FeedbackType, number> = {
      bug_report: 0,
      feature_request: 0,
      general_feedback: 0,
      support_request: 0,
      complaint: 0,
      compliment: 0,
      suggestion: 0,
    };

    typeResults.forEach((result) => {
      byType[result.type as FeedbackType] = result.count;
    });

    // Get counts by priority
    const priorityResults = await db
      .select({
        priority: feedback.priority,
        count: count(),
      })
      .from(feedback)
      .groupBy(feedback.priority);

    const byPriority: Record<FeedbackPriority, number> = {
      low: 0,
      medium: 0,
      high: 0,
      urgent: 0,
    };

    priorityResults.forEach((result) => {
      byPriority[result.priority as FeedbackPriority] = result.count;
    });

    // Get average rating
    const ratingResult = await db
      .select({
        avg: sql<number>`AVG(${feedback.rating})`,
      })
      .from(feedback)
      .where(sql`${feedback.rating} IS NOT NULL`);

    const averageRating = ratingResult[0]?.avg || 0;

    return {
      total,
      byStatus,
      byType,
      byPriority,
      averageRating: Math.round(averageRating * 100) / 100,
    };
  } catch (error) {
    console.error("Error getting feedback stats:", error);
    return {
      total: 0,
      byStatus: { open: 0, in_progress: 0, resolved: 0, closed: 0, duplicate: 0 },
      byType: {
        bug_report: 0,
        feature_request: 0,
        general_feedback: 0,
        support_request: 0,
        complaint: 0,
        compliment: 0,
        suggestion: 0,
      },
      byPriority: { low: 0, medium: 0, high: 0, urgent: 0 },
      averageRating: 0,
    };
  }
}

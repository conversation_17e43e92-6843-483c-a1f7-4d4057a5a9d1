<svg width="48" height="48" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="paymentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#059669"/>
      <stop offset="100%" style="stop-color:#10b981"/>
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="24" cy="24" r="20" fill="url(#paymentGradient)" opacity="0.1"/>
  
  <!-- Credit card -->
  <rect x="8" y="16" width="32" height="20" fill="url(#paymentGradient)" rx="4"/>
  <rect x="8" y="20" width="32" height="4" fill="#047857"/>
  
  <!-- Card details -->
  <rect x="12" y="26" width="16" height="2" fill="white" opacity="0.8" rx="1"/>
  <rect x="12" y="30" width="8" height="1.5" fill="white" opacity="0.6" rx="0.5"/>
  <rect x="32" y="30" width="4" height="1.5" fill="white" opacity="0.6" rx="0.5"/>
  
  <!-- Security chip -->
  <rect x="12" y="22" width="4" height="3" fill="#fbbf24" rx="0.5"/>
  
  <!-- Payment flow -->
  <circle cx="44" cy="12" r="3" fill="#10b981">
    <animate attributeName="opacity" values="0.3;1;0.3" dur="2s" repeatCount="indefinite"/>
  </circle>
  <path d="M40 14 L44 12 L40 10" stroke="#10b981" stroke-width="2" fill="none"/>
  
  <!-- Dollar sign -->
  <text x="24" y="12" fill="#059669" font-family="Arial, sans-serif" font-size="12" font-weight="bold" text-anchor="middle">$</text>
  
  <!-- Success checkmark -->
  <circle cx="24" cy="42" r="4" fill="#10b981"/>
  <path d="M22 42 L23.5 43.5 L26 41" stroke="white" stroke-width="2" fill="none"/>
</svg>

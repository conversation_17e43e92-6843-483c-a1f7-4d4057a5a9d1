import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/cloudflare";
import { json } from "@remix-run/cloudflare";
import { Form, useActionData, useLoaderData, useNavigation } from "@remix-run/react";
import {
  CheckCircle,
  Clock,
  FileText,
  Image,
  MessageSquare,
  Mic,
  XCircle,
  Zap,
} from "lucide-react";
import { useState } from "react";
import UnifiedLayout from "~/components/layout/unified-layout";
import { Alert, AlertDescription } from "~/components/ui/alert";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "~/components/ui/tabs";
import { Textarea } from "~/components/ui/textarea";
import { getAllCloudflareAIModels, validateCloudflareAI } from "~/lib/ai/cloudflare-ai";

export async function loader({ context }: LoaderFunctionArgs) {
  // Check if Cloudflare AI is available
  const isAvailable = validateCloudflareAI(context.cloudflare?.env);

  if (!isAvailable) {
    return json({
      available: false,
      models: {},
      error: "Cloudflare AI binding is not configured",
    });
  }

  const models = getAllCloudflareAIModels();

  return json({
    available: true,
    models,
    timestamp: new Date().toISOString(),
  });
}

export async function action({ request, context }: ActionFunctionArgs) {
  if (request.method !== "POST") {
    return json({ error: "Method not allowed" }, { status: 405 });
  }

  const formData = await request.formData();
  const action = formData.get("action") as string;

  try {
    // Forward the request to the Cloudflare AI API
    const response = await fetch(`${new URL(request.url).origin}/api/ai/cloudflare`, {
      method: "POST",
      body: formData,
    });

    const data = await response.json();

    return json({
      success: response.ok,
      data,
      action,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    return json({
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
      action,
      timestamp: new Date().toISOString(),
    });
  }
}

export default function CloudflareAIDemo() {
  const { available, models, error } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();

  const [selectedModel, setSelectedModel] = useState<string>("");
  const [prompt, setPrompt] = useState("Hello! Please tell me about Cloudflare Workers AI.");

  const isSubmitting = navigation.state === "submitting";

  if (!available) {
    return (
      <UnifiedLayout
        hero={{
          title: "Cloudflare AI Unavailable",
          description:
            "The Cloudflare AI service is not properly configured. Please check your environment setup.",
        }}
      >
        <section className="py-16">
          <div className="max-w-2xl mx-auto">
            <Card className="shadow-2xl border-0 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-red-600 dark:text-red-400">
                  <XCircle className="h-5 w-5" />
                  Configuration Issue
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Alert variant="destructive">
                  <XCircle className="h-4 w-4" />
                  <AlertDescription>
                    {error ||
                      "Cloudflare AI binding is not configured. Please check your wrangler.toml configuration."}
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>
          </div>
        </section>
      </UnifiedLayout>
    );
  }

  return (
    <UnifiedLayout
      hero={{
        title: "Cloudflare Workers AI Demo",
        description:
          "Experience the power of Cloudflare's AI models running at the edge. Test text generation, embeddings, classification, and image analysis with ultra-low latency.",
      }}
    >
      <section className="py-16">
        <div className="max-w-6xl mx-auto px-4 space-y-8">
          {/* Status Badge */}
          <div className="text-center">
            <Badge variant="default" className="bg-orange-500">
              <CheckCircle className="h-3 w-3 mr-1" />
              Cloudflare AI Available
            </Badge>
          </div>

          {/* Available Models */}
          <Card>
            <CardHeader>
              <CardTitle>Available AI Models</CardTitle>
              <CardDescription>
                Cloudflare Workers AI provides various models for different tasks
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {Object.entries(models).map(([category, modelList]) => (
                  <div key={category} className="space-y-2">
                    <h4 className="font-medium capitalize">
                      {category.replace(/([A-Z])/g, " $1").trim()}
                    </h4>
                    <div className="space-y-1">
                      {(modelList as string[]).map((model) => (
                        <Badge key={model} variant="outline" className="block text-xs">
                          {model}
                        </Badge>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Demo Interface */}
          <Card>
            <CardHeader>
              <CardTitle>Try Cloudflare AI</CardTitle>
              <CardDescription>
                Test different AI capabilities powered by Cloudflare Workers
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="text" className="space-y-6">
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="text" className="flex items-center gap-2">
                    <MessageSquare className="h-4 w-4" />
                    Text Generation
                  </TabsTrigger>
                  <TabsTrigger value="embeddings" className="flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    Embeddings
                  </TabsTrigger>
                  <TabsTrigger value="classification" className="flex items-center gap-2">
                    <Zap className="h-4 w-4" />
                    Text Classification
                  </TabsTrigger>
                  <TabsTrigger value="image" className="flex items-center gap-2">
                    <Image className="h-4 w-4" />
                    Image Classification
                  </TabsTrigger>
                </TabsList>

                {/* Text Generation */}
                <TabsContent value="text" className="space-y-4">
                  <Form method="post" className="space-y-4">
                    <input type="hidden" name="action" value="generate-text" />

                    <div className="space-y-2">
                      <label className="text-sm font-medium">Model</label>
                      <Select name="model" value={selectedModel} onValueChange={setSelectedModel}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a text generation model" />
                        </SelectTrigger>
                        <SelectContent>
                          {models.textGeneration?.map((model: string) => (
                            <SelectItem key={model} value={model}>
                              {model}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">Prompt</label>
                      <Textarea
                        name="prompt"
                        value={prompt}
                        onChange={(e) => setPrompt(e.target.value)}
                        placeholder="Enter your prompt..."
                        rows={4}
                      />
                    </div>

                    <Button
                      type="submit"
                      disabled={!selectedModel || !prompt || isSubmitting}
                      className="w-full"
                    >
                      {isSubmitting ? (
                        <>
                          <Clock className="h-4 w-4 mr-2 animate-spin" />
                          Generating...
                        </>
                      ) : (
                        <>
                          <Zap className="h-4 w-4 mr-2" />
                          Generate Text
                        </>
                      )}
                    </Button>
                  </Form>
                </TabsContent>

                {/* Embeddings */}
                <TabsContent value="embeddings" className="space-y-4">
                  <Form method="post" className="space-y-4">
                    <input type="hidden" name="action" value="generate-embeddings" />

                    <div className="space-y-2">
                      <label className="text-sm font-medium">Model</label>
                      <Select name="model">
                        <SelectTrigger>
                          <SelectValue placeholder="Select an embedding model" />
                        </SelectTrigger>
                        <SelectContent>
                          {models.textEmbeddings?.map((model: string) => (
                            <SelectItem key={model} value={model}>
                              {model}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">Text</label>
                      <Textarea
                        name="text"
                        placeholder="Enter text to generate embeddings..."
                        rows={3}
                      />
                    </div>

                    <Button type="submit" disabled={isSubmitting} className="w-full">
                      {isSubmitting ? (
                        <>
                          <Clock className="h-4 w-4 mr-2 animate-spin" />
                          Generating Embeddings...
                        </>
                      ) : (
                        <>
                          <FileText className="h-4 w-4 mr-2" />
                          Generate Embeddings
                        </>
                      )}
                    </Button>
                  </Form>
                </TabsContent>

                {/* Text Classification */}
                <TabsContent value="classification" className="space-y-4">
                  <Form method="post" className="space-y-4">
                    <input type="hidden" name="action" value="classify-text" />

                    <div className="space-y-2">
                      <label className="text-sm font-medium">Model</label>
                      <Select name="model">
                        <SelectTrigger>
                          <SelectValue placeholder="Select a classification model" />
                        </SelectTrigger>
                        <SelectContent>
                          {models.textClassification?.map((model: string) => (
                            <SelectItem key={model} value={model}>
                              {model}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">Text to Classify</label>
                      <Textarea
                        name="text"
                        placeholder="Enter text to classify (e.g., 'I love this product!' for sentiment analysis)..."
                        rows={3}
                      />
                    </div>

                    <Button type="submit" disabled={isSubmitting} className="w-full">
                      {isSubmitting ? (
                        <>
                          <Clock className="h-4 w-4 mr-2 animate-spin" />
                          Classifying...
                        </>
                      ) : (
                        <>
                          <Zap className="h-4 w-4 mr-2" />
                          Classify Text
                        </>
                      )}
                    </Button>
                  </Form>
                </TabsContent>

                {/* Image Classification */}
                <TabsContent value="image" className="space-y-4">
                  <Form method="post" encType="multipart/form-data" className="space-y-4">
                    <input type="hidden" name="action" value="classify-image" />

                    <div className="space-y-2">
                      <label className="text-sm font-medium">Model</label>
                      <Select name="model">
                        <SelectTrigger>
                          <SelectValue placeholder="Select an image classification model" />
                        </SelectTrigger>
                        <SelectContent>
                          {models.imageClassification?.map((model: string) => (
                            <SelectItem key={model} value={model}>
                              {model}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">Image</label>
                      <input
                        type="file"
                        name="image"
                        accept="image/*"
                        className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      />
                    </div>

                    <Button type="submit" disabled={isSubmitting} className="w-full">
                      {isSubmitting ? (
                        <>
                          <Clock className="h-4 w-4 mr-2 animate-spin" />
                          Classifying Image...
                        </>
                      ) : (
                        <>
                          <Image className="h-4 w-4 mr-2" />
                          Classify Image
                        </>
                      )}
                    </Button>
                  </Form>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>

          {/* Results */}
          {actionData && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  Results
                  <Badge variant={actionData.success ? "default" : "destructive"}>
                    {actionData.success ? (
                      <>
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Success
                      </>
                    ) : (
                      <>
                        <XCircle className="h-3 w-3 mr-1" />
                        Failed
                      </>
                    )}
                  </Badge>
                </CardTitle>
                <CardDescription>
                  Action: {actionData.action} | {actionData.timestamp}
                </CardDescription>
              </CardHeader>
              <CardContent>
                {actionData.success ? (
                  <div className="space-y-4">
                    <Alert>
                      <CheckCircle className="h-4 w-4" />
                      <AlertDescription>
                        Cloudflare AI operation completed successfully!
                      </AlertDescription>
                    </Alert>

                    <div className="space-y-2">
                      <h4 className="font-medium">Response:</h4>
                      <pre className="bg-muted p-4 rounded-lg text-sm overflow-auto max-h-96">
                        {JSON.stringify(actionData.data, null, 2)}
                      </pre>
                    </div>
                  </div>
                ) : (
                  <Alert variant="destructive">
                    <XCircle className="h-4 w-4" />
                    <AlertDescription>Error: {actionData.error}</AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>
          )}
        </div>
      </section>
    </UnifiedLayout>
  );
}

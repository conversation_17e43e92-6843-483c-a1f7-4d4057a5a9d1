import { Resend } from "resend";
import { resend<PERSON><PERSON><PERSON><PERSON> } from "./config";

if (!resendApiKey) {
  console.error("RESEND_API_KEY is not configured. Email functionality will be disabled.");
  // You might throw an error here to prevent the application from starting/running
  // if email is critical, or handle this state gracefully elsewhere.
  // For now, we log the error. The Resend constructor will throw if the key is truly missing.
}

// The Resend constructor will throw an error if resendApi<PERSON>ey is undefined or an empty string.
// We conditionally initialize to prevent an immediate crash if the key isn't set,
// allowing the app to potentially run with email features disabled.
// However, any attempt to use `resend` will fail if it's null.
let resend: Resend | null = null;

if (resendApi<PERSON>ey) {
  try {
    resend = new Resend(resendApiKey);
  } catch (error) {
    console.error("Failed to initialize Resend client:", error);
    // resend will remain null
  }
} else {
  console.warn("Resend client not initialized because RESEND_API_KEY is missing.");
}

export { resend };

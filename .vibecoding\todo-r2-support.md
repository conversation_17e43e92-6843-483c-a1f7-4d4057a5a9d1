# Task Issue Template

## Issue Title
完成 Cloudflare R2 对象存储集成和测试

## Description
### Current State
- 项目中已有部分 R2 存储相关代码
- `wrangler.toml` 中配置了 R2 bucket 绑定
- 存在 `app/routes/api.r2-*.tsx` 相关路由文件
- 但功能未经过完整测试，不确定是否可用

### Desired State
- 完成 R2 存储功能的完整集成
- 实现文件上传、下载、列表功能
- 测试所有 R2 相关 API 端点
- 确保与 AI 功能的图片存储需求对接

### Requirements Clarification
- [ ] **MANDATORY**: 确保 R2 bucket 配置正确
- [ ] **MANDATORY**: 测试文件上传下载功能完整性
- [ ] **MANDATORY**: 验证存储权限和安全性设置
- [ ] **MANDATORY**: 确保与现有 AI 图片生成功能集成

## Architecture Alignment
- [ ] **MANDATORY**: 确认 R2 集成符合现有存储架构
- [ ] 检查现有的 `app/lib/storage.ts` 或相关存储抽象
- [ ] 确保 R2 配置与 Cloudflare Workers 环境兼容
- [ ] 验证环境变量和 secrets 管理

## Technical Stack Compliance
**The following technical stack is FIXED and must be used:**
- [ ] React (UI framework)
- [ ] Remix (Full-stack framework)
- [ ] Neon (Database)
- [ ] Cloudflare (Hosting/Edge)
- [ ] R2 (Object storage)
- [ ] Biome (Linting/Formatting)
- [ ] TypeScript (Language)
- [ ] Yarn (Package manager)

## Implementation Checklist

### Pre-Development
- [ ] 检查现有 R2 相关代码结构
- [ ] 验证 `wrangler.toml` 中的 R2 配置
- [ ] 确认所需的环境变量设置
- [ ] 检查现有的 API 路由实现

### Development
- [ ] 完善 R2 存储服务类
- [ ] 实现文件上传功能 (`api.r2-upload.tsx`)
- [ ] 实现文件下载功能 (`api.r2-download.tsx`)
- [ ] 实现文件列表功能 (`api.r2-list.tsx`)
- [ ] 添加错误处理和验证
- [ ] 集成文件类型和大小限制

### Pre-Commit Checklist
- [ ] **MANDATORY**: Run `yarn format` to check code formatting
- [ ] **MANDATORY**: Run `yarn lint` to check for linting issues
- [ ] **MANDATORY**: Run `yarn typecheck` to ensure TypeScript compilation
- [ ] **MANDATORY**: Run `yarn test` to ensure all tests pass
- [ ] **MANDATORY**: Test R2 functionality with real uploads/downloads

### CI/CD Validation
- [ ] **MANDATORY**: Ensure CI pipeline passes before creating PR
- [ ] Verify R2 bindings work in deployment environment
- [ ] Test with actual Cloudflare Workers deployment
- [ ] Validate storage quotas and permissions

### Code Quality Standards
- [ ] 实现适当的错误处理
- [ ] 添加文件类型验证
- [ ] 实现文件大小限制
- [ ] 确保安全的文件访问控制
- [ ] 添加日志记录和监控

## Testing Requirements
- [ ] 单元测试覆盖 R2 服务类
- [ ] 集成测试验证上传下载流程
- [ ] 测试错误边界情况
- [ ] 验证大文件处理能力
- [ ] 测试并发上传场景

## Documentation
- [ ] 更新 R2 配置说明文档
- [ ] 添加 API 端点使用示例
- [ ] 记录环境变量配置要求
- [ ] 提供故障排除指南

## Acceptance Criteria
[具体可测量的完成标准]

1. **文件上传功能**: 用户可以通过 API 成功上传文件到 R2，返回文件 URL
2. **文件下载功能**: 用户可以通过生成的 URL 或 API 端点下载文件
3. **文件列表功能**: 可以获取用户上传的文件列表，包含文件元数据
4. **错误处理**: 上传失败、文件不存在等情况有适当的错误响应
5. **安全性**: 文件访问有适当的权限控制，不会泄露用户数据
6. **性能**: 支持至少 10MB 文件的上传下载，响应时间在合理范围内

## Additional Notes
- 确保 R2 bucket 在开发和生产环境都有正确配置
- 考虑实现文件的 CDN 缓存策略
- 为后续 AI 图片生成功能预留存储接口
- 注意 R2 的费用控制和配额管理

---

## Pre-PR Validation Commands
Before creating a Pull Request, run these commands in order:

```bash
# Format code
yarn format

# Check linting
yarn lint

# Type checking
yarn typecheck

# Run tests
yarn test

# Build to ensure compilation works
yarn build

# Test R2 functionality
curl -X POST http://localhost:8787/api/r2-upload -F "file=@test-image.jpg"
```

## CI Pipeline Verification
Ensure the GitHub Actions workflow (`.github/workflows/ci.yml`) passes locally before submitting PR.

---

**Remember**: Quality over speed. R2 存储是核心基础设施，必须确保稳定可靠。
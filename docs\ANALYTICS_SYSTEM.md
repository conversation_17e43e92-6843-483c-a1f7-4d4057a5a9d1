# Data Analytics Dashboard System

## Overview

The Data Analytics Dashboard System provides comprehensive analytics and insights for both users and administrators. It includes real-time metrics, trend analysis, performance monitoring, and detailed reporting capabilities with interactive visualizations and data export features.

## Features

### ✅ Implemented Features

#### 1. **User Analytics**
- **Personal Metrics**: Individual API usage, token consumption, and cost analysis
- **Performance Insights**: Response times, success rates, and optimization recommendations
- **Usage Breakdown**: Analysis by provider, model, endpoint, and status
- **Trend Analysis**: Daily and hourly usage patterns and trends
- **Recent Activity**: Real-time activity monitoring and detailed request logs
- **Cost Optimization**: Credit usage tracking and cost analysis

#### 2. **Admin Analytics**
- **System Overview**: Total users, revenue, API calls, and key business metrics
- **User Growth**: New user registrations, engagement, and retention analysis
- **Revenue Analytics**: Financial metrics, subscription tracking, and revenue trends
- **Usage Monitoring**: System-wide API usage, performance, and capacity planning
- **Top Users**: Most active users and usage patterns analysis
- **Feedback Analytics**: User satisfaction metrics and feedback trends

#### 3. **Data Visualization**
- **Interactive Charts**: Dynamic charts and graphs for trend visualization
- **Real-time Updates**: Live data updates and monitoring dashboards
- **Comparative Analysis**: Period-over-period comparison and growth tracking
- **Distribution Charts**: Usage distribution by various dimensions
- **Performance Metrics**: Response time and system performance visualization
- **Custom Filtering**: Flexible date ranges and filtering options

#### 4. **Reporting & Export**
- **Data Export**: Multiple export formats (JSON, CSV) with custom date ranges
- **Automated Reports**: Scheduled report generation and delivery
- **API Access**: Programmatic access to analytics data via REST API
- **Real-time Metrics**: Live system monitoring and alerting
- **Custom Dashboards**: Configurable dashboards for different user roles
- **Performance Optimization**: Efficient data processing and caching

## File Structure

```
app/
├── routes/
│   ├── console.analytics.tsx            # User analytics dashboard
│   ├── admin.analytics.tsx              # Admin analytics dashboard
│   ├── api.analytics.tsx                # Analytics API endpoints
│   └── test.analytics-system.tsx        # Development test page
├── services/
│   └── analytics.server.ts              # Core analytics service
└── docs/
    └── ANALYTICS_SYSTEM.md             # This documentation
```

## Database Integration

### Data Sources
The analytics system aggregates data from multiple tables:

```sql
-- API Usage tracking
api_usage (
  id, user_uuid, endpoint, method, provider, model,
  tokens_used, credits_used, duration, status, created_at
)

-- User information
users (
  uuid, name, email, created_at, updated_at
)

-- Financial data
orders (
  id, user_uuid, total_amount, status, created_at
)

-- Subscription data
subscriptions (
  id, user_uuid, plan_id, status, created_at
)

-- Credit transactions
credit_transactions (
  id, user_uuid, amount, type, created_at
)

-- Feedback data
feedback (
  id, user_uuid, type, status, rating, created_at
)
```

### Performance Optimization
- **Indexed Queries**: Optimized database queries with proper indexing
- **Aggregation**: Efficient data aggregation and summarization
- **Caching**: Strategic caching of frequently accessed analytics data
- **Pagination**: Efficient handling of large datasets

## API Endpoints

### Analytics API

#### Get User Analytics
```http
GET /api/analytics?action=user-analytics&period=30

Response:
{
  "code": 0,
  "message": "success",
  "data": {
    "analytics": {
      "overview": {
        "totalRequests": 1250,
        "successfulRequests": 1200,
        "failedRequests": 50,
        "successRate": 96.0,
        "totalTokens": 125000,
        "totalCredits": 1250,
        "totalCost": 12.50,
        "avgResponseTime": 850
      },
      "usage": {
        "byProvider": { "openai": 800, "anthropic": 450 },
        "byModel": { "gpt-4": 600, "claude-3": 400, "gpt-3.5": 250 },
        "byEndpoint": { "/api/ai/generate-text": 1000, "/api/ai/generate-image": 250 },
        "byStatus": { "success": 1200, "error": 30, "timeout": 20 }
      },
      "trends": {
        "dailyUsage": [
          {
            "date": "2024-01-15",
            "requests": 45,
            "tokens": 4500,
            "credits": 45,
            "cost": 0.45
          }
        ],
        "hourlyDistribution": [
          { "hour": 9, "requests": 120 },
          { "hour": 10, "requests": 150 }
        ]
      },
      "recentActivity": [
        {
          "id": "req_123",
          "endpoint": "/api/ai/generate-text",
          "method": "POST",
          "provider": "openai",
          "model": "gpt-4",
          "tokensUsed": 100,
          "creditsUsed": 1,
          "duration": 850,
          "status": "success",
          "createdAt": "2024-01-15T10:30:00Z"
        }
      ]
    },
    "period": {
      "days": 30,
      "startDate": "2023-12-16T00:00:00Z",
      "endDate": "2024-01-15T23:59:59Z"
    }
  }
}
```

#### Get Admin Analytics
```http
GET /api/analytics?action=admin-analytics&period=90&admin=true

Response:
{
  "code": 0,
  "message": "success",
  "data": {
    "analytics": {
      "overview": {
        "totalUsers": 1500,
        "activeUsers": 450,
        "totalRevenue": 25000.00,
        "monthlyRevenue": 8500.00,
        "totalOrders": 2800,
        "totalSubscriptions": 320,
        "totalApiCalls": 125000,
        "avgRevenuePerUser": 16.67
      },
      "users": {
        "newUsers": [
          { "date": "2024-01-15", "count": 12 }
        ],
        "userGrowth": {
          "total": 1500,
          "growth": 150,
          "growthRate": 11.1
        },
        "topUsers": [
          {
            "userUuid": "user-123",
            "userName": "John Doe",
            "userEmail": "<EMAIL>",
            "totalRequests": 5000,
            "totalCredits": 5000,
            "totalSpent": 50.00
          }
        ]
      },
      "revenue": {
        "dailyRevenue": [
          {
            "date": "2024-01-15",
            "revenue": 285.50,
            "orders": 15
          }
        ],
        "subscriptionMetrics": {
          "active": 320,
          "cancelled": 45,
          "churnRate": 12.3
        }
      },
      "usage": {
        "totalApiCalls": 125000,
        "byProvider": { "openai": 75000, "anthropic": 50000 },
        "topEndpoints": [
          {
            "endpoint": "/api/ai/generate-text",
            "requests": 100000,
            "avgDuration": 850
          }
        ]
      },
      "feedback": {
        "totalFeedback": 245,
        "byType": { "bug_report": 45, "feature_request": 120 },
        "byStatus": { "open": 30, "resolved": 200 },
        "averageRating": 4.2
      }
    }
  }
}
```

#### Get User Summary
```http
GET /api/analytics?action=user-summary&period=7

Response:
{
  "code": 0,
  "message": "success",
  "data": {
    "summary": {
      "totalRequests": 150,
      "successRate": 96.7,
      "totalCredits": 150,
      "totalCost": 1.50,
      "avgResponseTime": 820,
      "topProvider": "openai",
      "recentActivity": [...]
    }
  }
}
```

#### Real-time Metrics
```http
GET /api/analytics?action=real-time&admin=true

Response:
{
  "code": 0,
  "message": "success",
  "data": {
    "realTime": {
      "totalApiCalls": 45,
      "activeUsers": 12,
      "recentRevenue": 15.50,
      "systemHealth": "healthy"
    },
    "timestamp": "2024-01-15T10:30:00Z"
  }
}
```

#### Comparative Analysis
```http
GET /api/analytics?action=compare&period=30&compare_period=30&admin=true

Response:
{
  "code": 0,
  "message": "success",
  "data": {
    "comparison": {
      "current": { "totalUsers": 1500, "totalRevenue": 8500 },
      "previous": { "totalUsers": 1350, "totalRevenue": 7200 },
      "growth": {
        "users": 150,
        "revenue": 1300,
        "apiCalls": 15000
      }
    }
  }
}
```

#### Data Export
```http
GET /api/analytics?action=export&type=user&format=json&period=30

Response:
{
  "code": 0,
  "message": "success",
  "data": {
    "analytics": { ... },
    "exportInfo": {
      "type": "user",
      "format": "json",
      "generatedAt": "2024-01-15T10:30:00Z",
      "period": {
        "days": 30,
        "startDate": "2023-12-16T00:00:00Z",
        "endDate": "2024-01-15T23:59:59Z"
      }
    }
  }
}
```

## Core Services

### Analytics Service (`analytics.server.ts`)

#### Main Functions
```typescript
// Get comprehensive user analytics
getUserAnalytics(userUuid: string, dateRange: DateRange, db: Database): Promise<UserAnalytics>

// Get user analytics summary for dashboard
getUserAnalyticsSummary(userUuid: string, dateRange: DateRange, db: Database): Promise<UserSummary>

// Get system-wide admin analytics
getAdminAnalytics(dateRange: DateRange, db: Database): Promise<AdminAnalytics>
```

#### Data Types
```typescript
interface DateRange {
  startDate: Date;
  endDate: Date;
}

interface UserAnalytics {
  overview: {
    totalRequests: number;
    successfulRequests: number;
    failedRequests: number;
    successRate: number;
    totalTokens: number;
    totalCredits: number;
    totalCost: number;
    avgResponseTime: number;
  };
  usage: {
    byProvider: Record<string, number>;
    byModel: Record<string, number>;
    byEndpoint: Record<string, number>;
    byStatus: Record<string, number>;
  };
  trends: {
    dailyUsage: Array<DailyUsage>;
    hourlyDistribution: Array<HourlyUsage>;
  };
  recentActivity: Array<ActivityRecord>;
}

interface AdminAnalytics {
  overview: SystemOverview;
  users: UserMetrics;
  revenue: RevenueMetrics;
  usage: UsageMetrics;
  feedback: FeedbackMetrics;
}
```

## User Interface

### User Analytics Dashboard (`console.analytics.tsx`)

#### Features
- **Overview Cards**: Key metrics display with visual indicators
- **Tabbed Interface**: Organized sections for different analytics views
- **Interactive Charts**: Visual representation of usage trends and patterns
- **Time Period Selection**: Flexible date range selection (24h, 7d, 30d, 90d)
- **Real-time Updates**: Live data updates and refresh capabilities
- **Export Options**: Data export in multiple formats

#### Sections
1. **Overview**: High-level metrics and KPIs
2. **Usage Breakdown**: Detailed analysis by provider, model, endpoint
3. **Trends**: Time-based analysis and pattern identification
4. **Recent Activity**: Latest API calls and activity logs

### Admin Analytics Dashboard (`admin.analytics.tsx`)

#### Features
- **System Overview**: Comprehensive business metrics and KPIs
- **Multi-tab Interface**: Organized views for different analytics categories
- **Advanced Filtering**: Flexible filtering and date range selection
- **Real-time Monitoring**: Live system metrics and health monitoring
- **Export Capabilities**: Advanced reporting and data export options

#### Sections
1. **Overview**: System-wide metrics and business KPIs
2. **Users**: User growth, engagement, and top users analysis
3. **Revenue**: Financial metrics, subscription tracking, and revenue trends
4. **Usage**: API usage patterns, performance, and capacity planning
5. **Feedback**: User satisfaction metrics and feedback analysis

## Analytics Types

### User Analytics
- **Purpose**: Personal usage insights and optimization recommendations
- **Scope**: Individual user data and patterns
- **Metrics**: API usage, costs, performance, and activity
- **Access**: Available to all authenticated users

### Admin Analytics
- **Purpose**: System-wide insights and business intelligence
- **Scope**: Aggregated data across all users and system components
- **Metrics**: Business KPIs, growth metrics, and system performance
- **Access**: Restricted to administrators only

### Real-time Analytics
- **Purpose**: Live monitoring and immediate insights
- **Scope**: Current system state and recent activity
- **Metrics**: Active users, current load, and system health
- **Access**: Available to users and admins with different scopes

### Comparative Analytics
- **Purpose**: Period-over-period analysis and trend identification
- **Scope**: Historical data comparison and growth analysis
- **Metrics**: Growth rates, trend analysis, and performance changes
- **Access**: Available for both user and admin analytics

## Performance Optimization

### Database Optimization
- **Indexed Queries**: Optimized queries with proper database indexing
- **Aggregation**: Efficient data aggregation and pre-computed metrics
- **Query Optimization**: Optimized SQL queries for large datasets
- **Connection Pooling**: Efficient database connection management

### Caching Strategy
- **Analytics Caching**: Cache frequently accessed analytics data
- **Query Result Caching**: Cache expensive query results
- **Real-time Data**: Balance between real-time updates and performance
- **Cache Invalidation**: Smart cache invalidation strategies

### API Performance
- **Response Optimization**: Optimized API response sizes and structure
- **Pagination**: Efficient pagination for large datasets
- **Async Processing**: Background processing for heavy analytics operations
- **Rate Limiting**: API rate limiting to prevent abuse

## Security Considerations

### Data Access Control
- **User Isolation**: Users can only access their own analytics data
- **Admin Authentication**: Secure admin authentication and authorization
- **API Security**: Secure API endpoints with proper authentication
- **Data Privacy**: Protection of sensitive user information

### Data Protection
- **Anonymization**: Option to anonymize sensitive data in analytics
- **Audit Logging**: Comprehensive logging of analytics access
- **Data Retention**: Configurable data retention policies
- **Compliance**: GDPR and privacy regulation compliance

## Testing

### Test Page
Visit `/test/analytics-system` to:
- View implementation status and features
- Access testing scenarios and instructions
- Get API usage examples and documentation
- Review analytics types and capabilities

### Test Scenarios

#### 1. User Analytics Testing
1. Navigate to user analytics dashboard
2. Test different time period selections
3. Verify data accuracy and completeness
4. Test export functionality
5. Check real-time updates
6. Validate performance with large datasets

#### 2. Admin Analytics Testing
1. Access admin analytics dashboard
2. Review system overview and metrics
3. Test filtering and search capabilities
4. Verify user growth and revenue analytics
5. Check system usage and performance metrics
6. Test comparative analysis features

#### 3. API Testing
1. Test all analytics API endpoints
2. Verify authentication and authorization
3. Test different query parameters and filters
4. Check response formats and data structure
5. Test error handling and edge cases
6. Validate performance and response times

#### 4. Performance Testing
1. Test with large datasets and date ranges
2. Verify query performance and optimization
3. Test concurrent access and scalability
4. Monitor memory usage and resource consumption
5. Test caching effectiveness
6. Validate data consistency and accuracy

### Manual Testing Checklist

#### User Analytics
- [ ] Access user analytics dashboard
- [ ] Test time period selection
- [ ] Verify overview metrics accuracy
- [ ] Check usage breakdown charts
- [ ] Test trend analysis and patterns
- [ ] Verify recent activity logs
- [ ] Test export functionality

#### Admin Analytics
- [ ] Access admin analytics dashboard
- [ ] Review system overview metrics
- [ ] Test user growth analytics
- [ ] Check revenue and financial metrics
- [ ] Verify usage and performance data
- [ ] Test feedback analytics
- [ ] Check comparative analysis

#### API Endpoints
- [ ] Test user analytics API
- [ ] Test admin analytics API
- [ ] Test real-time metrics API
- [ ] Test comparative analysis API
- [ ] Test export functionality
- [ ] Verify error handling

## Future Enhancements

### Planned Features
- [ ] Advanced data visualization with interactive charts
- [ ] Custom dashboard creation and configuration
- [ ] Automated alerting and notification system
- [ ] Machine learning-based insights and predictions
- [ ] Advanced filtering and segmentation options
- [ ] Integration with external analytics platforms
- [ ] Mobile analytics dashboard
- [ ] Advanced export formats and scheduling

### Integration Opportunities
- [ ] Business intelligence platforms
- [ ] Data visualization tools (Tableau, Power BI)
- [ ] Monitoring and alerting systems
- [ ] Customer success platforms
- [ ] Financial reporting systems
- [ ] Machine learning platforms

## Troubleshooting

### Common Issues
1. **Slow query performance**: Check database indexes and query optimization
2. **Data inconsistencies**: Verify data aggregation logic and calculations
3. **Missing data**: Check data collection and storage processes
4. **Authentication errors**: Verify user permissions and API authentication
5. **Export failures**: Check data size limits and format compatibility

### Debug Tools
- **API Testing**: Use analytics API endpoints for debugging
- **Database Queries**: Direct database inspection for data verification
- **Performance Monitoring**: Monitor query performance and resource usage
- **Logging**: Comprehensive logging for troubleshooting and debugging

### Error Handling
- **Graceful Degradation**: System continues to function if analytics fail
- **Comprehensive Logging**: Detailed error logging for debugging
- **User-Friendly Messages**: Clear error messages for users
- **Fallback Data**: Fallback to cached or summary data when needed

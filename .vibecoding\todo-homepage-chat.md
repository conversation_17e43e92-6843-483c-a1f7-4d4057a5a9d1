# Task Issue Template

## Issue Title
首页改版：移除介绍内容，实现 ChatGPT 风格的聊天输入界面

## Description
### Current State
- 首页 (`app/routes/_index.tsx`) 当前展示完整的 landing page
- 包含 Hero 区域、特性展示、统计数据等营销内容
- 用户需要导航到专门页面才能使用 AI 功能

### Desired State
- 首页直接展示聊天输入界面，类似 ChatGPT 的简洁设计
- 移除或最小化 Hero 区域的营销内容
- 集成文本聊天和图片生成功能在一个界面
- 提供快速开始的用户体验

### Requirements Clarification
- [ ] **MANDATORY**: 确认是否完全移除 landing page 内容或保留部分
- [ ] **MANDATORY**: 确定聊天界面的具体功能范围（文本+图片）
- [ ] **MANDATORY**: 明确用户未登录时的交互行为
- [ ] **MANDATORY**: 确认是否需要保留现有的 landing page 在其他路由

## Architecture Alignment
- [ ] **MANDATORY**: 确认与现有 AI 路由的集成方式
- [ ] 检查现有的 AI API 端点 (`api.ai.*`) 可用性
- [ ] 确保与 Zustand 状态管理的兼容性
- [ ] 验证与用户认证系统的集成

## Technical Stack Compliance
**The following technical stack is FIXED and must be used:**
- [ ] React (UI framework)
- [ ] Remix (Full-stack framework)
- [ ] Neon (Database)
- [ ] Cloudflare (Hosting/Edge)
- [ ] R2 (Object storage)
- [ ] Biome (Linting/Formatting)
- [ ] TypeScript (Language)
- [ ] Yarn (Package manager)

## Implementation Checklist

### Pre-Development
- [ ] 分析现有 `_index.tsx` 的组件结构
- [ ] 检查现有 AI 相关组件和路由
- [ ] 设计新的聊天界面 UI/UX
- [ ] 确定组件复用策略

### Development
- [ ] 创建聊天界面主组件
- [ ] 实现消息输入组件
- [ ] 集成文本聊天功能
- [ ] 集成图片生成功能
- [ ] 实现功能切换（聊天/生图）
- [ ] 添加加载状态和错误处理
- [ ] 实现响应式设计
- [ ] 添加用户认证检查

### UI/UX Components
- [ ] 消息输入框（支持多行文本）
- [ ] 消息显示区域
- [ ] 功能切换按钮（聊天/生图）
- [ ] 发送按钮和快捷键支持
- [ ] 加载动画和状态指示
- [ ] 错误提示组件

### Pre-Commit Checklist
- [ ] **MANDATORY**: Run `yarn format` to check code formatting
- [ ] **MANDATORY**: Run `yarn lint` to check for linting issues
- [ ] **MANDATORY**: Run `yarn typecheck` to ensure TypeScript compilation
- [ ] **MANDATORY**: Run `yarn test` to ensure all tests pass
- [ ] **MANDATORY**: 测试聊天和生图功能完整性

### CI/CD Validation
- [ ] **MANDATORY**: Ensure CI pipeline passes before creating PR
- [ ] 验证部署后的功能可用性
- [ ] 测试移动端响应式效果
- [ ] 确认路由变更不影响 SEO

### Code Quality Standards
- [ ] 实现适当的错误边界
- [ ] 添加无障碍访问支持
- [ ] 优化加载性能
- [ ] 实现适当的用户反馈
- [ ] 确保数据安全和隐私保护

## Testing Requirements
- [ ] 测试聊天消息发送和接收
- [ ] 测试图片生成功能
- [ ] 测试功能切换的流畅性
- [ ] 验证错误处理和用户反馈
- [ ] 测试不同设备和浏览器兼容性
- [ ] 测试用户认证状态处理

## Documentation
- [ ] 更新组件文档
- [ ] 记录 API 集成方式
- [ ] 提供用户使用指南
- [ ] 更新路由文档

## Acceptance Criteria
[具体可测量的完成标准]

1. **界面改版**: 首页直接显示聊天输入界面，移除大部分营销内容
2. **文本聊天**: 用户可以输入文本并获得 AI 回复，显示在聊天窗口中
3. **图片生成**: 用户可以切换到图片生成模式，输入描述生成图片
4. **功能切换**: 用户可以在聊天和生图模式间无缝切换
5. **响应式设计**: 界面在桌面和移动设备上都有良好表现
6. **加载状态**: 提供清晰的加载指示和错误处理
7. **用户体验**: 整体交互流畅，类似 ChatGPT 的简洁体验

## Additional Notes
- 考虑是否需要保留原 landing page 在 `/about` 或 `/features` 路由
- 确保新界面对 SEO 的影响最小化
- 考虑添加使用引导或快速开始提示
- 注意 AI 功能的使用限制和用户提示
- 考虑实现对话历史的本地存储（如果用户未登录）

---

## Pre-PR Validation Commands
Before creating a Pull Request, run these commands in order:

```bash
# Format code
yarn format

# Check linting
yarn lint

# Type checking
yarn typecheck

# Run tests
yarn test

# Build to ensure compilation works
yarn build

# Test locally
yarn dev
# Navigate to http://localhost:3000 and test chat functionality
```

## CI Pipeline Verification
Ensure the GitHub Actions workflow (`.github/workflows/ci.yml`) passes locally before submitting PR.

---

**Remember**: 用户体验是关键，确保界面简洁直观，功能响应迅速。
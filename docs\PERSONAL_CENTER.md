# User Personal Center System

## Overview

The User Personal Center provides a comprehensive dashboard for users to manage their account, monitor usage, and access all personal information and settings. It's designed with a modern, responsive interface that works seamlessly across all devices.

## Features

### ✅ Implemented Features

#### 1. **Profile Management** (`/console/profile`)
- **Personal Information Editing**: Name, bio, and contact details
- **Avatar Management**: Upload via URL with preview functionality
- **Account Information**: User ID, creation date, and account statistics
- **Form Validation**: Real-time validation with error handling
- **Success Feedback**: Clear confirmation messages for updates

#### 2. **Credits Management** (`/console/credits`)
- **Real-time Balance**: Current credit balance with live updates
- **Transaction History**: Comprehensive history with filtering options
- **Usage Statistics**: Earned, spent, and net change analytics
- **Quick Purchase**: Direct links to credit packages
- **Transaction Categories**: Visual categorization of different transaction types

#### 3. **Usage Analytics** (`/console/usage`)
- **API Usage Metrics**: Detailed statistics and performance data
- **Provider Breakdown**: Usage by AI provider and model
- **Success Rate Monitoring**: Request success/failure analytics
- **Recent Activity**: Last 50 API calls with detailed information
- **Interactive Dashboard**: Charts and visualizations for usage trends

#### 4. **Account Settings** (`/console/settings`)
- **Notification Preferences**: Email, usage alerts, and security notifications
- **Invite Code Management**: Generate, regenerate, and share invite codes
- **Security Settings**: Overview of account security features
- **Privacy Controls**: Data and privacy preference management
- **Account Deletion**: Secure account deletion workflow (placeholder)

#### 5. **Console Layout System**
- **Responsive Sidebar**: Desktop and mobile-friendly navigation
- **Active Page Highlighting**: Clear visual indication of current page
- **Consistent Headers**: Standardized page titles and descriptions
- **Mobile Optimization**: Touch-friendly interface for mobile devices

## File Structure

```
app/
├── routes/
│   ├── console.profile.tsx          # Profile management page
│   ├── console.credits.tsx          # Credits and transactions
│   ├── console.usage.tsx            # Usage analytics
│   ├── console.settings.tsx         # Account settings
│   ├── api.user.update-profile.tsx  # Profile update API
│   └── test.personal-center.tsx     # Development test page
├── components/
│   ├── console/
│   │   └── console-layout.tsx       # Shared console layout
│   ├── auth/
│   │   └── user-menu.tsx           # Updated user menu
│   └── dashboard/
│       └── usage-analytics.tsx      # Usage analytics component
└── docs/
    └── PERSONAL_CENTER.md          # This documentation
```

## Page Details

### Profile Management (`/console/profile`)

**Features:**
- Edit personal information (name, bio)
- Update avatar via image URL
- View account statistics and metadata
- Real-time form validation

**API Integration:**
- `POST /api/user/update-profile` - Update user information
- `GET /console/profile` - Load user profile data

**Key Components:**
- Profile overview card with avatar and stats
- Editable form sections with validation
- Success/error message handling

### Credits Management (`/console/credits`)

**Features:**
- Current balance display with formatting
- Comprehensive transaction history
- Transaction filtering by time period
- Quick purchase options and packages
- Visual transaction categorization

**Data Sources:**
- User credit balance from database
- Transaction history with pagination
- Credit statistics and analytics

**Key Metrics:**
- Current balance, total earned, total spent
- Net change and transaction trends
- Popular credit packages

### Usage Analytics (`/console/usage`)

**Features:**
- Real-time API usage statistics
- Provider and model breakdowns
- Success rate monitoring
- Recent activity tracking
- Interactive analytics dashboard

**Integration:**
- Usage tracking system integration
- Real-time data updates
- Comprehensive analytics API

**Analytics Provided:**
- Total requests, tokens used, credits consumed
- Average response times
- Provider/model usage distribution
- Success/failure rate analysis

### Account Settings (`/console/settings`)

**Features:**
- Notification preference management
- Invite code generation and sharing
- Security settings overview
- Privacy controls
- Account deletion workflow

**Settings Categories:**
- **Notifications**: Email, usage alerts, security notifications
- **Security**: Two-factor authentication, API keys, password
- **Privacy**: Data preferences, marketing communications
- **Invite System**: Code generation, sharing, referral tracking

## Navigation System

### Desktop Navigation
- **Fixed Sidebar**: Always visible with full navigation
- **Active Highlighting**: Current page clearly indicated
- **Detailed Descriptions**: Each nav item includes description
- **Quick Access**: Direct links to all major sections

### Mobile Navigation
- **Collapsible Menu**: Space-efficient mobile sidebar
- **Touch Optimization**: Large touch targets
- **Gesture Support**: Swipe to open/close
- **Responsive Design**: Adapts to all screen sizes

## API Endpoints

### Profile Management
```typescript
POST /api/user/update-profile
{
  "action": "update-basic-info",
  "data": {
    "name": "John Doe",
    "bio": "Software developer"
  }
}
```

### Usage Analytics
```typescript
GET /api/usage/analytics?period=7&recent=true
// Returns comprehensive usage analytics
```

### Credit History
```typescript
GET /api/user/credit-history?page=1&limit=20
// Returns paginated credit transaction history
```

## Testing

### Test Page
Visit `/test/personal-center` to:
- Navigate to all personal center pages
- Test form submissions and validations
- Verify responsive design
- Check API integrations
- Validate error handling

### Manual Testing Checklist

#### Profile Management
- [ ] Update name and bio
- [ ] Change avatar URL
- [ ] Verify form validation
- [ ] Test error handling
- [ ] Check success messages

#### Credits Management
- [ ] View current balance
- [ ] Browse transaction history
- [ ] Test period filtering
- [ ] Check transaction details
- [ ] Verify purchase links

#### Usage Analytics
- [ ] View usage statistics
- [ ] Check provider breakdowns
- [ ] Test period selection
- [ ] Verify recent activity
- [ ] Test analytics dashboard

#### Settings
- [ ] Update notification preferences
- [ ] Generate new invite code
- [ ] Copy invite URL
- [ ] Test security settings
- [ ] Check account deletion flow

#### Navigation
- [ ] Test desktop sidebar
- [ ] Check mobile menu
- [ ] Verify active highlighting
- [ ] Test responsive behavior
- [ ] Check accessibility

## Security Considerations

### Data Protection
- User data validation and sanitization
- Secure avatar URL validation
- Protected API endpoints with authentication
- Input validation on all forms

### Privacy
- User-specific data access only
- Secure invite code generation
- Privacy-compliant data handling
- Optional data sharing controls

## Performance Optimizations

### Loading Strategies
- Lazy loading for heavy components
- Efficient data fetching with pagination
- Cached user data where appropriate
- Optimized image loading for avatars

### Responsive Design
- Mobile-first CSS approach
- Efficient grid layouts
- Touch-friendly interface elements
- Optimized for various screen sizes

## Future Enhancements

### Planned Features
- [ ] Two-factor authentication setup
- [ ] Advanced notification customization
- [ ] Data export functionality
- [ ] Profile picture upload to cloud storage
- [ ] Advanced usage analytics with charts
- [ ] Team management features
- [ ] Custom dashboard widgets

### Integration Opportunities
- [ ] Social media profile linking
- [ ] Third-party service integrations
- [ ] Advanced security features
- [ ] Billing and subscription management
- [ ] Support ticket system integration

## Troubleshooting

### Common Issues
1. **Profile updates not saving**: Check authentication and form validation
2. **Credits not updating**: Verify database connection and transaction processing
3. **Usage analytics not loading**: Check usage tracking system integration
4. **Mobile navigation issues**: Test responsive CSS and JavaScript

### Debug Tools
- Browser developer tools for frontend issues
- Server logs for API endpoint problems
- Database queries for data inconsistencies
- Network tab for API request/response debugging

## Accessibility

### Features Implemented
- Semantic HTML structure
- Keyboard navigation support
- Screen reader compatibility
- High contrast color schemes
- Focus indicators for interactive elements

### WCAG Compliance
- Level AA compliance target
- Alternative text for images
- Proper heading hierarchy
- Accessible form labels
- Color contrast requirements met

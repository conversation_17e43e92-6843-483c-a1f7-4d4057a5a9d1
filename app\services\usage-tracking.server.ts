/**
 * API Usage Tracking Service
 * Tracks API calls, token usage, costs, and provides analytics
 */

import { and, asc, avg, count, desc, eq, gte, lte, sql, sum } from "drizzle-orm";
import type { Database } from "~/lib/db/db";
import { apiUsage, rateLimits, usageStats, users } from "~/lib/db/schema";

export interface UsageTrackingData {
  userUuid: string;
  endpoint: string;
  method: string;
  provider?: string;
  model?: string;
  requestSize?: number;
  responseSize?: number;
  tokensUsed?: number;
  creditsUsed?: number;
  duration: number;
  status: "success" | "error" | "timeout";
  errorCode?: string;
  errorMessage?: string;
  ipAddress?: string;
  userAgent?: string;
  metadata?: Record<string, any>;
}

export interface UsageAnalytics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  totalTokens: number;
  totalCredits: number;
  totalCost: number;
  avgResponseTime: number;
  topProvider?: string;
  topModel?: string;
  topEndpoint?: string;
  requestsByProvider: Record<string, number>;
  requestsByModel: Record<string, number>;
  requestsByEndpoint: Record<string, number>;
  requestsByStatus: Record<string, number>;
  dailyUsage: Array<{
    date: string;
    requests: number;
    tokens: number;
    credits: number;
  }>;
}

export interface RateLimitConfig {
  endpoint: string;
  windowMinutes: number;
  maxRequests: number;
  maxTokens?: number;
  maxCredits?: number;
}

/**
 * Track API usage
 */
export async function trackApiUsage(
  data: UsageTrackingData,
  db: Database
): Promise<{ success: boolean; error?: string }> {
  try {
    await db.insert(apiUsage).values({
      userUuid: data.userUuid,
      endpoint: data.endpoint,
      method: data.method,
      provider: data.provider,
      model: data.model,
      requestSize: data.requestSize,
      responseSize: data.responseSize,
      tokensUsed: data.tokensUsed,
      creditsUsed: data.creditsUsed,
      duration: data.duration,
      status: data.status,
      errorCode: data.errorCode,
      errorMessage: data.errorMessage,
      ipAddress: data.ipAddress,
      userAgent: data.userAgent,
      metadata: data.metadata,
      createdAt: new Date(),
    });

    return { success: true };
  } catch (error) {
    console.error("Error tracking API usage:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

/**
 * Get user usage analytics for a date range
 */
export async function getUserUsageAnalytics(
  userUuid: string,
  startDate: Date,
  endDate: Date,
  db: Database
): Promise<UsageAnalytics> {
  try {
    // Get basic stats
    const basicStats = await db
      .select({
        totalRequests: count(),
        successfulRequests: sum(sql`CASE WHEN ${apiUsage.status} = 'success' THEN 1 ELSE 0 END`),
        failedRequests: sum(sql`CASE WHEN ${apiUsage.status} != 'success' THEN 1 ELSE 0 END`),
        totalTokens: sum(apiUsage.tokensUsed),
        totalCredits: sum(apiUsage.creditsUsed),
        avgResponseTime: avg(apiUsage.duration),
      })
      .from(apiUsage)
      .where(
        and(
          eq(apiUsage.userUuid, userUuid),
          gte(apiUsage.createdAt, startDate),
          lte(apiUsage.createdAt, endDate)
        )
      );

    // Get top provider
    const topProviderResult = await db
      .select({
        provider: apiUsage.provider,
        count: count(),
      })
      .from(apiUsage)
      .where(
        and(
          eq(apiUsage.userUuid, userUuid),
          gte(apiUsage.createdAt, startDate),
          lte(apiUsage.createdAt, endDate)
        )
      )
      .groupBy(apiUsage.provider)
      .orderBy(desc(count()))
      .limit(1);

    // Get top model
    const topModelResult = await db
      .select({
        model: apiUsage.model,
        count: count(),
      })
      .from(apiUsage)
      .where(
        and(
          eq(apiUsage.userUuid, userUuid),
          gte(apiUsage.createdAt, startDate),
          lte(apiUsage.createdAt, endDate)
        )
      )
      .groupBy(apiUsage.model)
      .orderBy(desc(count()))
      .limit(1);

    // Get top endpoint
    const topEndpointResult = await db
      .select({
        endpoint: apiUsage.endpoint,
        count: count(),
      })
      .from(apiUsage)
      .where(
        and(
          eq(apiUsage.userUuid, userUuid),
          gte(apiUsage.createdAt, startDate),
          lte(apiUsage.createdAt, endDate)
        )
      )
      .groupBy(apiUsage.endpoint)
      .orderBy(desc(count()))
      .limit(1);

    // Get requests by provider
    const providerStats = await db
      .select({
        provider: apiUsage.provider,
        count: count(),
      })
      .from(apiUsage)
      .where(
        and(
          eq(apiUsage.userUuid, userUuid),
          gte(apiUsage.createdAt, startDate),
          lte(apiUsage.createdAt, endDate)
        )
      )
      .groupBy(apiUsage.provider);

    // Get requests by model
    const modelStats = await db
      .select({
        model: apiUsage.model,
        count: count(),
      })
      .from(apiUsage)
      .where(
        and(
          eq(apiUsage.userUuid, userUuid),
          gte(apiUsage.createdAt, startDate),
          lte(apiUsage.createdAt, endDate)
        )
      )
      .groupBy(apiUsage.model);

    // Get requests by endpoint
    const endpointStats = await db
      .select({
        endpoint: apiUsage.endpoint,
        count: count(),
      })
      .from(apiUsage)
      .where(
        and(
          eq(apiUsage.userUuid, userUuid),
          gte(apiUsage.createdAt, startDate),
          lte(apiUsage.createdAt, endDate)
        )
      )
      .groupBy(apiUsage.endpoint);

    // Get requests by status
    const statusStats = await db
      .select({
        status: apiUsage.status,
        count: count(),
      })
      .from(apiUsage)
      .where(
        and(
          eq(apiUsage.userUuid, userUuid),
          gte(apiUsage.createdAt, startDate),
          lte(apiUsage.createdAt, endDate)
        )
      )
      .groupBy(apiUsage.status);

    // Get daily usage
    const dailyUsage = await db
      .select({
        date: sql<string>`DATE(${apiUsage.createdAt})`,
        requests: count(),
        tokens: sum(apiUsage.tokensUsed),
        credits: sum(apiUsage.creditsUsed),
      })
      .from(apiUsage)
      .where(
        and(
          eq(apiUsage.userUuid, userUuid),
          gte(apiUsage.createdAt, startDate),
          lte(apiUsage.createdAt, endDate)
        )
      )
      .groupBy(sql`DATE(${apiUsage.createdAt})`)
      .orderBy(asc(sql`DATE(${apiUsage.createdAt})`));

    const stats = basicStats[0];

    return {
      totalRequests: Number(stats.totalRequests) || 0,
      successfulRequests: Number(stats.successfulRequests) || 0,
      failedRequests: Number(stats.failedRequests) || 0,
      totalTokens: Number(stats.totalTokens) || 0,
      totalCredits: Number(stats.totalCredits) || 0,
      totalCost: 0, // TODO: Calculate based on provider pricing
      avgResponseTime: Number(stats.avgResponseTime) || 0,
      topProvider: topProviderResult[0]?.provider || undefined,
      topModel: topModelResult[0]?.model || undefined,
      topEndpoint: topEndpointResult[0]?.endpoint || undefined,
      requestsByProvider: Object.fromEntries(
        providerStats.map((stat) => [stat.provider || "unknown", Number(stat.count)])
      ),
      requestsByModel: Object.fromEntries(
        modelStats.map((stat) => [stat.model || "unknown", Number(stat.count)])
      ),
      requestsByEndpoint: Object.fromEntries(
        endpointStats.map((stat) => [stat.endpoint, Number(stat.count)])
      ),
      requestsByStatus: Object.fromEntries(
        statusStats.map((stat) => [stat.status, Number(stat.count)])
      ),
      dailyUsage: dailyUsage.map((day) => ({
        date: day.date,
        requests: Number(day.requests),
        tokens: Number(day.tokens) || 0,
        credits: Number(day.credits) || 0,
      })),
    };
  } catch (error) {
    console.error("Error getting usage analytics:", error);
    return {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      totalTokens: 0,
      totalCredits: 0,
      totalCost: 0,
      avgResponseTime: 0,
      requestsByProvider: {},
      requestsByModel: {},
      requestsByEndpoint: {},
      requestsByStatus: {},
      dailyUsage: [],
    };
  }
}

/**
 * Check rate limits for a user and endpoint
 */
export async function checkRateLimit(
  userUuid: string,
  endpoint: string,
  config: RateLimitConfig,
  db: Database
): Promise<{
  allowed: boolean;
  remaining: number;
  resetTime: Date;
  current: {
    requests: number;
    tokens: number;
    credits: number;
  };
}> {
  try {
    const now = new Date();
    const windowStart = new Date(now.getTime() - config.windowMinutes * 60 * 1000);

    // Get current usage in the window
    const currentUsage = await db
      .select({
        requests: count(),
        tokens: sum(apiUsage.tokensUsed),
        credits: sum(apiUsage.creditsUsed),
      })
      .from(apiUsage)
      .where(
        and(
          eq(apiUsage.userUuid, userUuid),
          eq(apiUsage.endpoint, endpoint),
          gte(apiUsage.createdAt, windowStart)
        )
      );

    const usage = currentUsage[0];
    const currentRequests = Number(usage.requests) || 0;
    const currentTokens = Number(usage.tokens) || 0;
    const currentCredits = Number(usage.credits) || 0;

    // Check limits
    const requestsAllowed = currentRequests < config.maxRequests;
    const tokensAllowed = !config.maxTokens || currentTokens < config.maxTokens;
    const creditsAllowed = !config.maxCredits || currentCredits < config.maxCredits;

    const allowed = requestsAllowed && tokensAllowed && creditsAllowed;
    const remaining = Math.max(0, config.maxRequests - currentRequests);
    const resetTime = new Date(windowStart.getTime() + config.windowMinutes * 60 * 1000);

    return {
      allowed,
      remaining,
      resetTime,
      current: {
        requests: currentRequests,
        tokens: currentTokens,
        credits: currentCredits,
      },
    };
  } catch (error) {
    console.error("Error checking rate limit:", error);
    // Allow request on error to avoid blocking users
    return {
      allowed: true,
      remaining: config.maxRequests,
      resetTime: new Date(Date.now() + config.windowMinutes * 60 * 1000),
      current: {
        requests: 0,
        tokens: 0,
        credits: 0,
      },
    };
  }
}

/**
 * Get recent API usage for a user
 */
export async function getRecentUsage(
  userUuid: string,
  limit = 50,
  db: Database
): Promise<
  Array<{
    id: string;
    endpoint: string;
    method: string;
    provider?: string;
    model?: string;
    tokensUsed?: number;
    creditsUsed?: number;
    duration: number;
    status: string;
    createdAt: string;
  }>
> {
  try {
    const usage = await db
      .select({
        id: apiUsage.id,
        endpoint: apiUsage.endpoint,
        method: apiUsage.method,
        provider: apiUsage.provider,
        model: apiUsage.model,
        tokensUsed: apiUsage.tokensUsed,
        creditsUsed: apiUsage.creditsUsed,
        duration: apiUsage.duration,
        status: apiUsage.status,
        createdAt: apiUsage.createdAt,
      })
      .from(apiUsage)
      .where(eq(apiUsage.userUuid, userUuid))
      .orderBy(desc(apiUsage.createdAt))
      .limit(limit);

    return usage.map((item) => ({
      id: item.id,
      endpoint: item.endpoint,
      method: item.method,
      provider: item.provider || undefined,
      model: item.model || undefined,
      tokensUsed: item.tokensUsed || undefined,
      creditsUsed: item.creditsUsed || undefined,
      duration: item.duration,
      status: item.status,
      createdAt: item.createdAt.toISOString(),
    }));
  } catch (error) {
    console.error("Error getting recent usage:", error);
    return [];
  }
}

/**
 * Calculate cost based on usage and provider pricing
 */
export function calculateUsageCost(
  provider: string,
  model: string,
  tokensUsed: number,
  operation: "text" | "image" | "embedding" = "text"
): number {
  // Simplified pricing model - in production, this would be more sophisticated
  const pricing: Record<string, Record<string, number>> = {
    openai: {
      "gpt-4o": 0.00003, // per token
      "gpt-4o-mini": 0.000001,
      "gpt-3.5-turbo": 0.000001,
      "dall-e-3": 0.04, // per image
      "dall-e-2": 0.02,
    },
    deepseek: {
      "deepseek-chat": 0.000001,
      "deepseek-coder": 0.000001,
    },
    cloudflare: {
      "llama-3.2-3b": 0.0000005,
      "llama-3-8b": 0.000001,
    },
  };

  const providerPricing = pricing[provider];
  if (!providerPricing) return 0;

  const modelPrice = providerPricing[model];
  if (!modelPrice) return 0;

  if (operation === "image") {
    return modelPrice; // Fixed price per image
  }

  return tokensUsed * modelPrice;
}

/**
 * Generate usage summary for a period
 */
export async function generateUsageSummary(
  userUuid: string,
  period: "daily" | "weekly" | "monthly",
  date: Date,
  db: Database
): Promise<{
  success: boolean;
  summary?: {
    period: string;
    periodStart: Date;
    periodEnd: Date;
    totalRequests: number;
    successfulRequests: number;
    failedRequests: number;
    totalTokens: number;
    totalCredits: number;
    totalCost: number;
    avgResponseTime: number;
    topProvider?: string;
    topModel?: string;
    topEndpoint?: string;
  };
  error?: string;
}> {
  try {
    let periodStart: Date;
    let periodEnd: Date;

    // Calculate period boundaries
    switch (period) {
      case "daily":
        periodStart = new Date(date.getFullYear(), date.getMonth(), date.getDate());
        periodEnd = new Date(periodStart.getTime() + 24 * 60 * 60 * 1000);
        break;
      case "weekly":
        const dayOfWeek = date.getDay();
        periodStart = new Date(date.getTime() - dayOfWeek * 24 * 60 * 60 * 1000);
        periodStart = new Date(
          periodStart.getFullYear(),
          periodStart.getMonth(),
          periodStart.getDate()
        );
        periodEnd = new Date(periodStart.getTime() + 7 * 24 * 60 * 60 * 1000);
        break;
      case "monthly":
        periodStart = new Date(date.getFullYear(), date.getMonth(), 1);
        periodEnd = new Date(date.getFullYear(), date.getMonth() + 1, 1);
        break;
    }

    const analytics = await getUserUsageAnalytics(userUuid, periodStart, periodEnd, db);

    const summary = {
      period,
      periodStart,
      periodEnd,
      totalRequests: analytics.totalRequests,
      successfulRequests: analytics.successfulRequests,
      failedRequests: analytics.failedRequests,
      totalTokens: analytics.totalTokens,
      totalCredits: analytics.totalCredits,
      totalCost: analytics.totalCost,
      avgResponseTime: analytics.avgResponseTime,
      topProvider: analytics.topProvider,
      topModel: analytics.topModel,
      topEndpoint: analytics.topEndpoint,
    };

    return { success: true, summary };
  } catch (error) {
    console.error("Error generating usage summary:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

# Email Module (`app/lib/email/`)

This module provides functionalities for sending emails using the Resend service. It includes a core email service, a templating system, and API integration for sending emails.

## Features

-   Integration with Resend API for email dispatch.
-   Core email service (`emailService`) for sending emails.
-   Basic templating system for reusable email content.
-   Predefined templates for common scenarios (e.g., Welcome, Password Reset).
-   API endpoint (`/api/email/send`) for triggering email sends.
-   TypeScript types for clear interfaces and type safety.
-   Configuration via environment variables for API keys.

## Directory Structure

-   `config.ts`: Manages Resend API key configuration from environment variables.
-   `resend.server.ts`: Initializes and exports the Resend client.
-   `service.server.ts`: Contains the core `emailService.sendEmail` function.
-   `templating.server.ts`: Provides a basic templating utility (`populateTemplate`) and `EmailTemplate` interface.
-   `types.ts`: Defines TypeScript interfaces for email parameters, recipients, etc.
-   `templates/`: Directory containing email template definitions.
    -   `index.ts`: Exports all templates and provides a `getTemplate` utility.
    -   `welcome.ts`: Example "Welcome" email template.
    -   `password-reset.ts`: Example "Password Reset" email template.
-   `README.md`: This documentation file.

## Configuration

1.  **Resend API Key:**
    The module requires a Resend API key. This key should be set as an environment variable named `RESEND_API_KEY`.
    -   **Local Development:** Add `RESEND_API_KEY="your_actual_resend_api_key"` to your `.dev.vars` file in the project root. Wrangler will load this into `process.env`.
    -   **Cloudflare Worker Deployment:** Set `RESEND_API_KEY` as a secret in your Worker's settings (via the Cloudflare dashboard or `wrangler.toml`). The `config.ts` file attempts to read this from `process.env.RESEND_API_KEY`. Ensure your Cloudflare environment makes this binding available as such (see comments in `config.ts` for more details).

2.  **Verified 'From' Address:**
    All emails sent via Resend must use a "From" address associated with a domain you have verified in your Resend account. Update the default `fromAddress` in `app/routes/api/email/send.tsx` or ensure any "from" address provided in the API payload is from a verified domain.
    ```typescript
    // Example in app/routes/api/email/send.tsx:
    const fromAddress: EmailRecipient = payload.from || { email: "<EMAIL>", name: "Your Platform Name" };
    ```

## Usage

### Sending Emails via API

An API endpoint is available at `POST /api/email/send` to send emails.

**Request Body Schema:**

```json
{
  "templateName": "welcome" | "passwordReset", // Name of the template to use
  "to": {
    "email": "<EMAIL>",
    "name": "Recipient Name" // Optional
  },
  "from": { // Optional: if not provided, API route uses a default. MUST be a verified Resend sender.
    "email": "<EMAIL>",
    "name": "Sender Name" // Optional
  },
  "templateVariables": { // Optional: key-value pairs for template placeholders
    "name": "John Doe",
    "resetLink": "https://example.com/reset/token123"
    // Add other variables as required by the specific template
  }
}
```

**Example Curl Request:**

```bash
curl -X POST \
  http://localhost:8787/api/email/send \
  -H 'Content-Type: application/json' \
  -d '{
        "templateName": "welcome",
        "to": { "email": "<EMAIL>", "name": "Test Recipient" },
        "templateVariables": { "name": "Test User" }
      }'
```
*(Note: Ensure your local dev server is running, typically via `yarn dev` or `wrangler dev`)*

### Using `emailService` Directly (Server-Side)

You can also use the `emailService` directly in your server-side Remix loaders or actions if you need more control or are sending emails not directly triggered by an API call.

```typescript
import { emailService } from '~/lib/email/service.server';
import { welcomeTemplate } from '~/lib/email/templates'; // Or use getTemplate('welcome')
import type { EmailRecipient } from '~/lib/email/types';

// Example in a Remix action:
export const action = async ({ request, context }: ActionFunctionArgs) => {
  // ...
  const userEmail = "<EMAIL>";
  const userName = "Jane Doe";

  const template = welcomeTemplate; // Or: getTemplate('welcome');
  if (!template) {
    // Handle template not found
    return;
  }

  const templateVariables = { name: userName };

  const result = await emailService.sendEmail({
    to: { email: userEmail, name: userName },
    // IMPORTANT: Replace with your verified Resend sender address
    from: { email: '<EMAIL>', name: 'Your App' },
    subject: template.subject(templateVariables),
    html: template.html(templateVariables),
    text: template.text(templateVariables),
    // Optional params:
    // replyTo: { email: '<EMAIL>' },
    // cc: [{ email: '<EMAIL>' }],
    // tags: [{ name: 'EmailType', value: 'WelcomeEmail' }]
  });

  if (result.success) {
    console.log('Email sent directly via service. Message ID:', result.messageId);
  } else {
    console.error('Failed to send email directly:', result.error);
  }
  // ...
};
```

## Email Templates

Email templates are defined in `app/lib/email/templates/`. Each template file should export an object conforming to the `EmailTemplate` interface (from `app/lib/email/templating.server.ts`).

**`EmailTemplate` Interface:**

```typescript
interface EmailTemplate {
  subject: (vars?: TemplateVariables) => string;
  html: (vars?: TemplateVariables) => string;
  text: (vars?: TemplateVariables) => string;
}
```

**Creating a New Template:**

1.  Create a new file, e.g., `app/lib/email/templates/my-new-template.ts`.
2.  Implement the template structure:
    ```typescript
    import { populateTemplate } from '../templating.server';
    import type { EmailTemplate } from '../templating.server';

    const subjectTemplate = 'Subject with {{variable}}';
    const htmlTemplate = '<h1>HTML {{variable}}</h1>';
    const textTemplate = 'Text {{variable}}';

    export const myNewTemplate: EmailTemplate = {
      subject: (vars) => populateTemplate(subjectTemplate, vars || {}),
      html: (vars) => populateTemplate(htmlTemplate, vars || {}),
      text: (vars) => populateTemplate(textTemplate, vars || {}),
    };
    ```
3.  Export the new template from `app/lib/email/templates/index.ts`:
    ```typescript
    // In app/lib/email/templates/index.ts
    export { myNewTemplate } from './my-new-template';
    // ...
    export type TemplateName = 'welcome' | 'passwordReset' | 'myNewTemplate'; // Add to type
    // ...
    export const templates: Record<TemplateName, EmailTemplate> = {
      // ...
      myNewTemplate: myNewTemplate, // Add to map
    };
    ```
4.  Update the `templateName` Zod schema in `app/routes/api/email/send.tsx` if you want the new template to be available via the API.

## Error Handling

-   The `emailService.sendEmail` function returns a promise resolving to `{ success: boolean; messageId?: string; error?: string }`.
-   API route `app/routes/api/email/send.tsx` provides JSON responses with appropriate status codes and error messages.
-   Check server logs for detailed error information.

## Future Enhancements

-   More sophisticated HTML templating using `@react-email/render` (package is installed).
-   Advanced unsubscribe handling for marketing emails.
-   Integration with Resend's suppression lists or audience features.
-   More detailed error parsing from Resend API responses.
```

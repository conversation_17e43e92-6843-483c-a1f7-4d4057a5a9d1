import path from "path";
/// <reference types="vitest" />
import { cloudflareDevProxyVitePlugin, vitePlugin as remix } from "@remix-run/dev";
import { defineConfig } from "vite";
import tsconfigPaths from "vite-tsconfig-paths";
import { getLoadContext } from "./load-context";

declare module "@remix-run/cloudflare" {
  interface Future {
    v3_singleFetch: true;
  }
}

export default defineConfig({
  // Exclude .000 directory from all processing
  resolve: {
    alias: {
      "~": path.resolve(__dirname, "./app"),
    },
    mainFields: ["browser", "module", "main"],
  },
  plugins: [
    cloudflareDevProxyVitePlugin({
      getLoadContext: getLoadContext as (args: any) => any,
    }),
    remix({
      future: {
        v3_fetcherPersist: true,
        v3_relativeSplatPath: true,
        v3_throwAbortReason: true,
        v3_singleFetch: true,
        v3_lazyRouteDiscovery: true,
      },
      // Exclude .000 directory from Remix processing
      ignoredRouteFiles: ["**/.*", "**/*.css", "**/*.test.{js,jsx,ts,tsx}", ".000/**/*"],
    }),
    tsconfigPaths({
      // Exclude .000 directory from TypeScript path resolution
      projects: ["./tsconfig.json"],
      ignoreConfigErrors: true,
    }),
    // Bundle analyzer - only when ANALYZE=true (disabled for Cloudflare Workers)
    // Note: Bundle analyzer is disabled for Cloudflare Workers compatibility
    // ...(process.env.ANALYZE === "true" ? [analyzer({ analyzerMode: "server", openAnalyzer: true })] : []),
  ],
  ssr: {
    resolve: {
      conditions: ["workerd", "worker", "browser"],
    },
    noExternal: ["@stackframe/react"],
  },
  optimizeDeps: {
    include: ["@stackframe/react"],
    exclude: [],
  },
  build: {
    minify: true,
    // Enhanced build configuration for performance
    rollupOptions: {
      output: {
        // Manual chunk splitting for better caching
        manualChunks: (id) => {
          // Only apply manual chunks for client build
          if (id.includes("node_modules")) {
            if (id.includes("react") || id.includes("react-dom")) {
              return "vendor";
            }
            if (id.includes("@radix-ui")) {
              return "ui";
            }
            if (id.includes("clsx") || id.includes("tailwind-merge")) {
              return "utils";
            }
            if (id.includes("react-i18next") || id.includes("i18next")) {
              return "i18n";
            }
            if (id.includes("lucide-react")) {
              return "icons";
            }
          }
        },
      },
    },
    // Source maps for production debugging (simplified for Cloudflare Workers)
    sourcemap: "hidden",
    // Chunk size warning limit
    chunkSizeWarningLimit: 500,
  },
});

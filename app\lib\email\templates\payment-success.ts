/**
 * Payment Success Email Template
 * Sent when a payment is successfully processed
 */

import type { EmailTemplate } from "../templating.server";

export interface PaymentSuccessTemplateVariables {
  name: string;
  amount: string;
  currency: string;
  planName: string;
  creditsAdded: number;
  invoiceUrl?: string;
  nextBillingDate?: string;
  subscriptionUrl?: string;
}

export const paymentSuccessTemplate: EmailTemplate<PaymentSuccessTemplateVariables> = {
  subject: (variables) => `Payment Confirmed - ${variables.planName} Subscription`,

  html: (variables) => `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Payment Confirmed</title>
      <style>
        body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
          line-height: 1.6;
          color: #333;
          max-width: 600px;
          margin: 0 auto;
          padding: 20px;
          background-color: #f8fafc;
        }
        .container {
          background-color: white;
          border-radius: 8px;
          padding: 32px;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .header {
          text-align: center;
          margin-bottom: 32px;
        }
        .logo {
          font-size: 24px;
          font-weight: bold;
          color: #2563eb;
          margin-bottom: 8px;
        }
        .success-badge {
          display: inline-block;
          background-color: #dcfce7;
          color: #166534;
          padding: 8px 16px;
          border-radius: 20px;
          font-size: 14px;
          font-weight: 600;
          margin-bottom: 16px;
        }
        .title {
          font-size: 28px;
          font-weight: bold;
          margin-bottom: 8px;
          color: #111827;
        }
        .subtitle {
          font-size: 16px;
          color: #6b7280;
          margin-bottom: 32px;
        }
        .payment-details {
          background-color: #f9fafb;
          border: 1px solid #e5e7eb;
          border-radius: 8px;
          padding: 24px;
          margin: 24px 0;
        }
        .detail-row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 8px 0;
          border-bottom: 1px solid #e5e7eb;
        }
        .detail-row:last-child {
          border-bottom: none;
          font-weight: 600;
          font-size: 18px;
        }
        .detail-label {
          color: #6b7280;
        }
        .detail-value {
          font-weight: 600;
          color: #111827;
        }
        .credits-highlight {
          background: linear-gradient(135deg, #fbbf24, #f59e0b);
          color: white;
          padding: 16px;
          border-radius: 8px;
          text-align: center;
          margin: 24px 0;
        }
        .credits-number {
          font-size: 32px;
          font-weight: bold;
          margin-bottom: 4px;
        }
        .action-buttons {
          text-align: center;
          margin: 32px 0;
        }
        .button {
          display: inline-block;
          padding: 12px 24px;
          margin: 8px;
          text-decoration: none;
          border-radius: 6px;
          font-weight: 600;
          font-size: 14px;
        }
        .button-primary {
          background-color: #2563eb;
          color: white;
        }
        .button-secondary {
          background-color: #f3f4f6;
          color: #374151;
          border: 1px solid #d1d5db;
        }
        .button:hover {
          opacity: 0.9;
        }
        .footer {
          margin-top: 32px;
          padding-top: 24px;
          border-top: 1px solid #e5e7eb;
          text-align: center;
          font-size: 14px;
          color: #6b7280;
        }
        .next-billing {
          background-color: #eff6ff;
          border: 1px solid #bfdbfe;
          border-radius: 6px;
          padding: 16px;
          margin: 16px 0;
          text-align: center;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <div class="logo">Your App</div>
          <div class="success-badge">✓ Payment Successful</div>
        </div>
        
        <div class="title">Thank you for your payment!</div>
        <div class="subtitle">
          Your ${variables.planName} subscription is now active and ready to use.
        </div>
        
        <div class="payment-details">
          <div class="detail-row">
            <span class="detail-label">Plan</span>
            <span class="detail-value">${variables.planName}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">Amount Paid</span>
            <span class="detail-value">${variables.amount} ${variables.currency.toUpperCase()}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">Credits Added</span>
            <span class="detail-value">${variables.creditsAdded.toLocaleString()} credits</span>
          </div>
          ${
            variables.nextBillingDate
              ? `
          <div class="detail-row">
            <span class="detail-label">Next Billing Date</span>
            <span class="detail-value">${variables.nextBillingDate}</span>
          </div>
          `
              : ""
          }
        </div>
        
        <div class="credits-highlight">
          <div class="credits-number">${variables.creditsAdded.toLocaleString()}</div>
          <div>AI credits added to your account</div>
        </div>
        
        <div class="action-buttons">
          ${
            variables.subscriptionUrl
              ? `
            <a href="${variables.subscriptionUrl}" class="button button-primary">
              Manage Subscription
            </a>
          `
              : ""
          }
          ${
            variables.invoiceUrl
              ? `
            <a href="${variables.invoiceUrl}" class="button button-secondary">
              Download Invoice
            </a>
          `
              : ""
          }
        </div>
        
        ${
          variables.nextBillingDate
            ? `
        <div class="next-billing">
          <strong>Next billing:</strong> ${variables.nextBillingDate}<br>
          You'll receive another ${variables.creditsAdded.toLocaleString()} credits on your next billing cycle.
        </div>
        `
            : ""
        }
        
        <div class="footer">
          <p>Hi ${variables.name},</p>
          <p>Your payment has been successfully processed and your subscription is now active.</p>
          <p>You can start using your AI credits immediately. If you have any questions about your subscription or need help getting started, our support team is here to help.</p>
          <p>Thank you for choosing Your App!</p>
        </div>
      </div>
    </body>
    </html>
  `,

  text: (variables) => `
Payment Confirmed - ${variables.planName} Subscription

Hi ${variables.name},

Thank you for your payment! Your ${variables.planName} subscription is now active.

Payment Details:
- Plan: ${variables.planName}
- Amount Paid: ${variables.amount} ${variables.currency.toUpperCase()}
- Credits Added: ${variables.creditsAdded.toLocaleString()} credits
${variables.nextBillingDate ? `- Next Billing Date: ${variables.nextBillingDate}` : ""}

Your ${variables.creditsAdded.toLocaleString()} AI credits have been added to your account and are ready to use.

${variables.subscriptionUrl ? `Manage your subscription: ${variables.subscriptionUrl}` : ""}
${variables.invoiceUrl ? `Download invoice: ${variables.invoiceUrl}` : ""}

${
  variables.nextBillingDate
    ? `
Next billing: ${variables.nextBillingDate}
You'll receive another ${variables.creditsAdded.toLocaleString()} credits on your next billing cycle.
`
    : ""
}

Your payment has been successfully processed and your subscription is now active.
You can start using your AI credits immediately.

If you have any questions about your subscription or need help getting started, 
our support team is here to help.

Thank you for choosing Your App!

---
Your App Team
  `,
};

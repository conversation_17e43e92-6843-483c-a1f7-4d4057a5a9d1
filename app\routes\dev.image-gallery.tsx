import UnifiedLayout from "~/components/layout/unified-layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";

export default function DevImageGallery() {
  const showcaseImages = [
    { name: "AI Chat Interface", path: "/images/showcase/ai-chat.svg" },
    { name: "Database Analytics", path: "/images/showcase/database.svg" },
    { name: "Dark Mode Toggle", path: "/images/showcase/dark-mode.svg" },
    { name: "Authentication Flow", path: "/images/showcase/auth.svg" },
    { name: "Payment Integration", path: "/images/showcase/payments.svg" },
    { name: "Component Library", path: "/images/showcase/components.svg" },
  ];

  const featureIcons = [
    { name: "AI Integration", path: "/images/icons/ai.svg" },
    { name: "Remix Framework", path: "/images/icons/remix.svg" },
    { name: "Cloudflare Edge", path: "/images/icons/cloudflare.svg" },
    { name: "Neon Database", path: "/images/icons/database.svg" },
    { name: "Beautiful UI", path: "/images/icons/ui.svg" },
    { name: "Payments Ready", path: "/images/icons/payments.svg" },
  ];

  const stepIcons = [
    { name: "Download & Setup", path: "/images/icons/download.svg" },
    { name: "Customize", path: "/images/icons/customize.svg" },
    { name: "Deploy", path: "/images/icons/deploy.svg" },
  ];

  const avatars = [
    { name: "Alex Chen", path: "/images/avatars/alex.svg" },
    { name: "Sarah Johnson", path: "/images/avatars/sarah.svg" },
    { name: "Mike Rodriguez", path: "/images/avatars/mike.svg" },
  ];

  return (
    <UnifiedLayout
      hero={{
        title: "Image Gallery",
        description:
          "Explore all the custom images, icons, and visual assets created for the project. From hero backgrounds to feature icons and user avatars.",
      }}
    >
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 space-y-8">
          {/* Hero Background */}
          <Card>
            <CardHeader>
              <CardTitle>Hero Background</CardTitle>
              <CardDescription>Animated SVG background for the hero section</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="relative h-64 rounded-lg overflow-hidden border">
                <img
                  src="/images/hero-bg.svg"
                  alt="Hero Background"
                  className="w-full h-full object-cover"
                />
              </div>
            </CardContent>
          </Card>

          {/* Showcase Images */}
          <Card>
            <CardHeader>
              <CardTitle>Showcase Images</CardTitle>
              <CardDescription>Feature demonstration mockups</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {showcaseImages.map((image, index) => (
                  <div key={index} className="space-y-2">
                    <div className="aspect-video bg-gray-100 dark:bg-gray-800 rounded-lg overflow-hidden border">
                      <img
                        src={image.path}
                        alt={image.name}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <p className="text-sm font-medium text-center">{image.name}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Feature Icons */}
          <Card>
            <CardHeader>
              <CardTitle>Feature Icons</CardTitle>
              <CardDescription>Custom SVG icons for features</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
                {featureIcons.map((icon, index) => (
                  <div key={index} className="space-y-2 text-center">
                    <div className="w-16 h-16 mx-auto bg-gradient-to-r from-blue-500 to-purple-500 rounded-2xl flex items-center justify-center">
                      <img src={icon.path} alt={icon.name} className="w-10 h-10" />
                    </div>
                    <p className="text-xs font-medium">{icon.name}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Step Icons */}
          <Card>
            <CardHeader>
              <CardTitle>Usage Step Icons</CardTitle>
              <CardDescription>Icons for the getting started steps</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {stepIcons.map((icon, index) => (
                  <div key={index} className="space-y-2 text-center">
                    <div className="w-20 h-20 mx-auto bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                      <img src={icon.path} alt={icon.name} className="w-12 h-12" />
                    </div>
                    <p className="text-sm font-medium">{icon.name}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Avatar Images */}
          <Card>
            <CardHeader>
              <CardTitle>User Avatars</CardTitle>
              <CardDescription>Testimonial user avatars</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {avatars.map((avatar, index) => (
                  <div key={index} className="space-y-2 text-center">
                    <div className="w-16 h-16 mx-auto">
                      <img
                        src={avatar.path}
                        alt={avatar.name}
                        className="w-full h-full rounded-full object-cover ring-2 ring-blue-500/20"
                      />
                    </div>
                    <p className="text-sm font-medium">{avatar.name}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </section>
    </UnifiedLayout>
  );
}

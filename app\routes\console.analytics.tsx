/**
 * User Analytics Dashboard
 * Comprehensive analytics and insights for user usage patterns
 */

import type { LoaderFunctionArgs } from "@remix-run/cloudflare";
import { json } from "@remix-run/cloudflare";
import { useLoaderData, useSearchParams } from "@remix-run/react";
import {
  Activity,
  AlertTriangle,
  BarChart3,
  Calendar,
  CheckCircle,
  Clock,
  DollarSign,
  Download,
  Filter,
  RefreshCw,
  TrendingUp,
  XCircle,
  Zap,
} from "lucide-react";
import { useState } from "react";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { createDbFromEnv } from "~/lib/db";
import { type DateRange, type UserAnalytics, getUserAnalytics } from "~/services/analytics.server";
import { getUserUuid } from "~/services/user";

export async function loader({ request, context }: LoaderFunctionArgs) {
  try {
    const db = context.db || createDbFromEnv(context.cloudflare?.env);

    const userUuid = await getUserUuid();
    if (!userUuid) {
      return json({ success: false, error: "Not authenticated" }, { status: 401 });
    }

    const url = new URL(request.url);
    const period = url.searchParams.get("period") || "7";
    const days = parseInt(period, 10);

    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const dateRange: DateRange = { startDate, endDate };
    const analytics = await getUserAnalytics(userUuid, dateRange, db);

    return json({
      success: true,
      data: {
        analytics,
        period: {
          days,
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
        },
      },
    });
  } catch (error) {
    console.error("Error loading user analytics:", error);
    return json({ success: false, error: "Failed to load analytics" }, { status: 500 });
  }
}

export default function UserAnalyticsPage() {
  const { data } = useLoaderData<typeof loader>();
  const [searchParams, setSearchParams] = useSearchParams();
  const [selectedTab, setSelectedTab] = useState("overview");

  const analytics: UserAnalytics = data.analytics;
  const period = data.period;

  const handlePeriodChange = (newPeriod: string) => {
    const newParams = new URLSearchParams(searchParams);
    newParams.set("period", newPeriod);
    setSearchParams(newParams);
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + "M";
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + "K";
    }
    return num.toString();
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "success":
        return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";
      case "error":
        return "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400";
      case "timeout":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "success":
        return <CheckCircle className="w-4 h-4" />;
      case "error":
        return <XCircle className="w-4 h-4" />;
      case "timeout":
        return <AlertTriangle className="w-4 h-4" />;
      default:
        return <Clock className="w-4 h-4" />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                Analytics Dashboard
              </h1>
              <p className="mt-2 text-gray-600 dark:text-gray-400">
                Comprehensive insights into your API usage and performance
              </p>
            </div>
            <div className="flex items-center space-x-3">
              <Select value={period.days.toString()} onValueChange={handlePeriodChange}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">Last 24h</SelectItem>
                  <SelectItem value="7">Last 7 days</SelectItem>
                  <SelectItem value="30">Last 30 days</SelectItem>
                  <SelectItem value="90">Last 90 days</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline" size="sm">
                <Download className="w-4 h-4 mr-2" />
                Export
              </Button>
              <Button variant="outline" size="sm">
                <RefreshCw className="w-4 h-4 mr-2" />
                Refresh
              </Button>
            </div>
          </div>
        </div>

        {/* Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Activity className="w-8 h-8 text-blue-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Total Requests
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {formatNumber(analytics.overview.totalRequests)}
                  </p>
                  <p className="text-xs text-green-600">
                    {analytics.overview.successRate.toFixed(1)}% success rate
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Zap className="w-8 h-8 text-yellow-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Tokens Used
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {formatNumber(analytics.overview.totalTokens)}
                  </p>
                  <p className="text-xs text-gray-500">
                    {formatNumber(analytics.overview.totalCredits)} credits
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <DollarSign className="w-8 h-8 text-green-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Cost</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {formatCurrency(analytics.overview.totalCost)}
                  </p>
                  <p className="text-xs text-gray-500">
                    Avg:{" "}
                    {formatCurrency(
                      analytics.overview.totalCost / Math.max(analytics.overview.totalRequests, 1)
                    )}{" "}
                    per request
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Clock className="w-8 h-8 text-purple-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Avg Response
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {analytics.overview.avgResponseTime}ms
                  </p>
                  <p className="text-xs text-gray-500">Response time</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Detailed Analytics */}
        <Tabs value={selectedTab} onValueChange={setSelectedTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="usage">Usage Breakdown</TabsTrigger>
            <TabsTrigger value="trends">Trends</TabsTrigger>
            <TabsTrigger value="activity">Recent Activity</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Success Rate */}
              <Card>
                <CardHeader>
                  <CardTitle>Request Status Distribution</CardTitle>
                  <CardDescription>Breakdown of request statuses</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {Object.entries(analytics.usage.byStatus).map(([status, count]) => (
                      <div key={status} className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          {getStatusIcon(status)}
                          <span className="capitalize">{status}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className="font-medium">{count}</span>
                          <Badge className={getStatusColor(status)}>
                            {((count / analytics.overview.totalRequests) * 100).toFixed(1)}%
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Top Providers */}
              <Card>
                <CardHeader>
                  <CardTitle>Provider Usage</CardTitle>
                  <CardDescription>API usage by provider</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {Object.entries(analytics.usage.byProvider)
                      .sort(([, a], [, b]) => b - a)
                      .slice(0, 5)
                      .map(([provider, count]) => (
                        <div key={provider} className="flex items-center justify-between">
                          <span className="capitalize">{provider}</span>
                          <div className="flex items-center space-x-2">
                            <span className="font-medium">{count}</span>
                            <Badge variant="outline">
                              {((count / analytics.overview.totalRequests) * 100).toFixed(1)}%
                            </Badge>
                          </div>
                        </div>
                      ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="usage" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Models */}
              <Card>
                <CardHeader>
                  <CardTitle>Model Usage</CardTitle>
                  <CardDescription>API usage by AI model</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {Object.entries(analytics.usage.byModel)
                      .sort(([, a], [, b]) => b - a)
                      .slice(0, 10)
                      .map(([model, count]) => (
                        <div key={model} className="flex items-center justify-between">
                          <span className="text-sm">{model}</span>
                          <div className="flex items-center space-x-2">
                            <span className="font-medium">{count}</span>
                            <Badge variant="outline">
                              {((count / analytics.overview.totalRequests) * 100).toFixed(1)}%
                            </Badge>
                          </div>
                        </div>
                      ))}
                  </div>
                </CardContent>
              </Card>

              {/* Endpoints */}
              <Card>
                <CardHeader>
                  <CardTitle>Endpoint Usage</CardTitle>
                  <CardDescription>API usage by endpoint</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {Object.entries(analytics.usage.byEndpoint)
                      .sort(([, a], [, b]) => b - a)
                      .slice(0, 10)
                      .map(([endpoint, count]) => (
                        <div key={endpoint} className="flex items-center justify-between">
                          <span className="text-sm font-mono">{endpoint}</span>
                          <div className="flex items-center space-x-2">
                            <span className="font-medium">{count}</span>
                            <Badge variant="outline">
                              {((count / analytics.overview.totalRequests) * 100).toFixed(1)}%
                            </Badge>
                          </div>
                        </div>
                      ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="trends" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Daily Usage */}
              <Card>
                <CardHeader>
                  <CardTitle>Daily Usage Trend</CardTitle>
                  <CardDescription>API usage over time</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {analytics.trends.dailyUsage.slice(-7).map((day) => (
                      <div key={day.date} className="flex items-center justify-between">
                        <span className="text-sm">{new Date(day.date).toLocaleDateString()}</span>
                        <div className="flex items-center space-x-4 text-sm">
                          <span>{day.requests} requests</span>
                          <span>{formatNumber(day.tokens)} tokens</span>
                          <span>{formatCurrency(day.cost)}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Hourly Distribution */}
              <Card>
                <CardHeader>
                  <CardTitle>Hourly Distribution</CardTitle>
                  <CardDescription>Usage patterns by hour</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {analytics.trends.hourlyDistribution.map((hour) => (
                      <div key={hour.hour} className="flex items-center justify-between">
                        <span className="text-sm">{hour.hour}:00</span>
                        <div className="flex items-center space-x-2">
                          <div className="w-20 bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-blue-500 h-2 rounded-full"
                              style={{
                                width: `${(hour.requests / Math.max(...analytics.trends.hourlyDistribution.map((h) => h.requests))) * 100}%`,
                              }}
                            />
                          </div>
                          <span className="text-sm font-medium w-8">{hour.requests}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="activity" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Recent API Activity</CardTitle>
                <CardDescription>Your latest API calls and their details</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {analytics.recentActivity.map((activity) => (
                    <div
                      key={activity.id}
                      className="flex items-center justify-between p-4 border rounded-lg"
                    >
                      <div className="flex items-center space-x-4">
                        {getStatusIcon(activity.status)}
                        <div>
                          <p className="font-medium">
                            {activity.method} {activity.endpoint}
                          </p>
                          <div className="flex items-center space-x-2 text-sm text-gray-500">
                            {activity.provider && <span>{activity.provider}</span>}
                            {activity.model && <span>• {activity.model}</span>}
                            <span>• {new Date(activity.createdAt).toLocaleString()}</span>
                          </div>
                        </div>
                      </div>
                      <div className="text-right text-sm">
                        <div className="flex items-center space-x-4">
                          <span>{activity.duration}ms</span>
                          {activity.tokensUsed && <span>{activity.tokensUsed} tokens</span>}
                          {activity.creditsUsed && <span>{activity.creditsUsed} credits</span>}
                          <Badge className={getStatusColor(activity.status)}>
                            {activity.status}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}

/**
 * Admin Feedback Management Page
 * Allows administrators to view, manage, and respond to user feedback
 */

import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/cloudflare";
import { json } from "@remix-run/cloudflare";
import {
  Form,
  useActionData,
  useLoaderData,
  useNavigation,
  useSearchParams,
} from "@remix-run/react";
import {
  AlertTriangle,
  BarChart3,
  Bug,
  Calendar,
  CheckCircle,
  Clock,
  Edit3,
  Filter,
  Frown,
  HelpCircle,
  Lightbulb,
  MessageSquare,
  Search,
  Smile,
  Star,
  Trash2,
  User,
  XCircle,
  Zap,
} from "lucide-react";
import { useState } from "react";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import { Textarea } from "~/components/ui/textarea";
import { createDbFromEnv } from "~/lib/db";
import {
  type FeedbackPriority,
  type FeedbackStatus,
  type FeedbackType,
  type FeedbackWithUser,
  deleteFeedback,
  getFeedback,
  getFeedbackStats,
  updateFeedback,
} from "~/services/feedback.server";

export async function loader({ request, context }: LoaderFunctionArgs) {
  try {
    const db = context.db || createDbFromEnv(context.cloudflare?.env);

    // TODO: Add admin authentication check

    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get("page") || "1", 10);
    const limit = parseInt(url.searchParams.get("limit") || "20", 10);
    const status = url.searchParams.get("status") as FeedbackStatus | null;
    const type = url.searchParams.get("type") as FeedbackType | null;
    const priority = url.searchParams.get("priority") as FeedbackPriority | null;
    const search = url.searchParams.get("search") || undefined;

    const [feedbackData, stats] = await Promise.all([
      getFeedback(
        {
          page,
          limit,
          status: status || undefined,
          type: type || undefined,
          priority: priority || undefined,
          search,
        },
        db
      ),
      getFeedbackStats(db),
    ]);

    return json({
      success: true,
      data: {
        feedback: feedbackData.feedback,
        pagination: feedbackData.pagination,
        stats,
      },
    });
  } catch (error) {
    console.error("Error loading admin feedback:", error);
    return json({ success: false, error: "Failed to load feedback" }, { status: 500 });
  }
}

export async function action({ request, context }: ActionFunctionArgs) {
  try {
    const db = context.db || createDbFromEnv(context.cloudflare?.env);

    // TODO: Add admin authentication check

    const formData = await request.formData();
    const action = formData.get("action") as string;
    const feedbackId = parseInt(formData.get("feedbackId") as string, 10);

    switch (action) {
      case "update": {
        const status = formData.get("status") as FeedbackStatus;
        const priority = formData.get("priority") as FeedbackPriority;
        const adminNotes = formData.get("adminNotes") as string;
        const assignedTo = formData.get("assignedTo") as string;

        const result = await updateFeedback(
          feedbackId,
          {
            status,
            priority,
            adminNotes,
            assignedTo: assignedTo || undefined,
          },
          db
        );

        if (result.success) {
          return json({
            success: true,
            message: "Feedback updated successfully",
          });
        } else {
          return json({
            success: false,
            error: result.error,
          });
        }
      }

      case "delete": {
        const result = await deleteFeedback(feedbackId, db);

        if (result.success) {
          return json({
            success: true,
            message: "Feedback deleted successfully",
          });
        } else {
          return json({
            success: false,
            error: result.error,
          });
        }
      }

      default:
        return json({ success: false, error: "Invalid action" });
    }
  } catch (error) {
    console.error("Error processing admin feedback action:", error);
    return json({ success: false, error: "Failed to process request" }, { status: 500 });
  }
}

export default function AdminFeedbackPage() {
  const { data } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();
  const [searchParams, setSearchParams] = useSearchParams();

  const [editingFeedback, setEditingFeedback] = useState<number | null>(null);
  const [showFilters, setShowFilters] = useState(false);

  const isSubmitting = navigation.state === "submitting";

  const feedbackTypes = [
    { value: "bug_report", label: "Bug Report", icon: Bug, color: "text-red-500" },
    { value: "feature_request", label: "Feature Request", icon: Lightbulb, color: "text-blue-500" },
    {
      value: "general_feedback",
      label: "General Feedback",
      icon: MessageSquare,
      color: "text-gray-500",
    },
    {
      value: "support_request",
      label: "Support Request",
      icon: HelpCircle,
      color: "text-purple-500",
    },
    { value: "complaint", label: "Complaint", icon: Frown, color: "text-red-500" },
    { value: "compliment", label: "Compliment", icon: Smile, color: "text-green-500" },
    { value: "suggestion", label: "Suggestion", icon: Zap, color: "text-yellow-500" },
  ];

  const statusOptions = [
    {
      value: "open",
      label: "Open",
      color: "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400",
    },
    {
      value: "in_progress",
      label: "In Progress",
      color: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400",
    },
    {
      value: "resolved",
      label: "Resolved",
      color: "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",
    },
    {
      value: "closed",
      label: "Closed",
      color: "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400",
    },
    {
      value: "duplicate",
      label: "Duplicate",
      color: "bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400",
    },
  ];

  const priorityLevels = [
    {
      value: "low",
      label: "Low",
      color: "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400",
    },
    {
      value: "medium",
      label: "Medium",
      color: "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400",
    },
    {
      value: "high",
      label: "High",
      color: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400",
    },
    {
      value: "urgent",
      label: "Urgent",
      color: "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400",
    },
  ];

  const getStatusColor = (status: string) => {
    const statusInfo = statusOptions.find((s) => s.value === status);
    return statusInfo?.color || "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "open":
        return <Clock className="w-4 h-4" />;
      case "in_progress":
        return <AlertTriangle className="w-4 h-4" />;
      case "resolved":
        return <CheckCircle className="w-4 h-4" />;
      case "closed":
        return <XCircle className="w-4 h-4" />;
      case "duplicate":
        return <XCircle className="w-4 h-4" />;
      default:
        return <Clock className="w-4 h-4" />;
    }
  };

  const getTypeIcon = (type: string) => {
    const typeInfo = feedbackTypes.find((t) => t.value === type);
    if (typeInfo) {
      const Icon = typeInfo.icon;
      return <Icon className={`w-4 h-4 ${typeInfo.color}`} />;
    }
    return <MessageSquare className="w-4 h-4 text-gray-500" />;
  };

  const getPriorityColor = (priority: string) => {
    const priorityInfo = priorityLevels.find((p) => p.value === priority);
    return (
      priorityInfo?.color || "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400"
    );
  };

  const renderStars = (rating: number) => {
    return (
      <div className="flex space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`w-4 h-4 ${
              star <= rating ? "text-yellow-400 fill-current" : "text-gray-300"
            }`}
          />
        ))}
      </div>
    );
  };

  const handleSearch = (search: string) => {
    const newParams = new URLSearchParams(searchParams);
    if (search) {
      newParams.set("search", search);
    } else {
      newParams.delete("search");
    }
    newParams.delete("page"); // Reset to first page
    setSearchParams(newParams);
  };

  const handleFilter = (key: string, value: string) => {
    const newParams = new URLSearchParams(searchParams);
    if (value && value !== "all") {
      newParams.set(key, value);
    } else {
      newParams.delete(key);
    }
    newParams.delete("page"); // Reset to first page
    setSearchParams(newParams);
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                Feedback Management
              </h1>
              <p className="mt-2 text-gray-600 dark:text-gray-400">
                Manage and respond to user feedback
              </p>
            </div>
            <Button variant="outline" onClick={() => setShowFilters(!showFilters)}>
              <Filter className="w-4 h-4 mr-2" />
              Filters
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <BarChart3 className="w-8 h-8 text-blue-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Total Feedback
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {data.stats.total}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Clock className="w-8 h-8 text-yellow-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Open</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {data.stats.byStatus.open}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="w-8 h-8 text-green-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Resolved</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {data.stats.byStatus.resolved}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Star className="w-8 h-8 text-yellow-400" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Avg Rating</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {data.stats.averageRating}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Action Messages */}
        {actionData && (
          <div
            className={`mb-6 p-4 rounded-lg flex items-center space-x-2 ${
              actionData.success
                ? "bg-green-50 text-green-800 border border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800"
                : "bg-red-50 text-red-800 border border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800"
            }`}
          >
            {actionData.success ? (
              <CheckCircle className="w-5 h-5" />
            ) : (
              <XCircle className="w-5 h-5" />
            )}
            <span>{actionData.message || actionData.error}</span>
          </div>
        )}

        {/* Search and Filters */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex items-center space-x-4 mb-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="Search feedback..."
                    className="pl-10"
                    defaultValue={searchParams.get("search") || ""}
                    onChange={(e) => handleSearch(e.target.value)}
                  />
                </div>
              </div>
            </div>

            {showFilters && (
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 pt-4 border-t">
                <div>
                  <Label>Status</Label>
                  <Select
                    value={searchParams.get("status") || "all"}
                    onValueChange={(value) => handleFilter("status", value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Statuses</SelectItem>
                      {statusOptions.map((status) => (
                        <SelectItem key={status.value} value={status.value}>
                          {status.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>Type</Label>
                  <Select
                    value={searchParams.get("type") || "all"}
                    onValueChange={(value) => handleFilter("type", value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Types</SelectItem>
                      {feedbackTypes.map((type) => (
                        <SelectItem key={type.value} value={type.value}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>Priority</Label>
                  <Select
                    value={searchParams.get("priority") || "all"}
                    onValueChange={(value) => handleFilter("priority", value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Priorities</SelectItem>
                      {priorityLevels.map((priority) => (
                        <SelectItem key={priority.value} value={priority.value}>
                          {priority.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-end">
                  <Button
                    variant="outline"
                    onClick={() => {
                      setSearchParams({});
                      setShowFilters(false);
                    }}
                  >
                    Clear Filters
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Feedback List */}
        <Card>
          <CardHeader>
            <CardTitle>Feedback Items</CardTitle>
            <CardDescription>
              {data.feedback.length === 0
                ? "No feedback found"
                : `${data.feedback.length} feedback item${data.feedback.length === 1 ? "" : "s"} found`}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {data.feedback.length === 0 ? (
              <div className="text-center py-12">
                <MessageSquare className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                <h3 className="text-lg font-semibold mb-2">No Feedback Found</h3>
                <p className="text-gray-600 dark:text-gray-400">
                  No feedback matches your current filters
                </p>
              </div>
            ) : (
              <div className="space-y-6">
                {data.feedback.map((feedback: FeedbackWithUser) => (
                  <div
                    key={feedback.id}
                    className="p-6 border rounded-lg bg-white dark:bg-gray-800"
                  >
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-3 mb-2">
                          <h4 className="font-semibold text-gray-900 dark:text-white">
                            {feedback.title}
                          </h4>
                          <div className="flex items-center space-x-2">
                            {getTypeIcon(feedback.type)}
                            <Badge className={getStatusColor(feedback.status)}>
                              <div className="flex items-center space-x-1">
                                {getStatusIcon(feedback.status)}
                                <span className="capitalize">
                                  {feedback.status.replace("_", " ")}
                                </span>
                              </div>
                            </Badge>
                            <Badge className={getPriorityColor(feedback.priority)}>
                              {feedback.priority}
                            </Badge>
                          </div>
                        </div>

                        <p className="text-gray-600 dark:text-gray-400 mb-3">{feedback.content}</p>

                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-500 mb-4">
                          <div className="flex items-center space-x-2">
                            <User className="w-4 h-4" />
                            <span>
                              {feedback.user?.name ||
                                feedback.userName ||
                                feedback.userEmail ||
                                "Anonymous"}
                            </span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Calendar className="w-4 h-4" />
                            <span>{new Date(feedback.createdAt).toLocaleDateString()}</span>
                          </div>
                          {feedback.rating && (
                            <div className="flex items-center space-x-2">
                              <span>Rating:</span>
                              {renderStars(feedback.rating)}
                            </div>
                          )}
                          {feedback.resolvedAt && (
                            <div className="flex items-center space-x-2">
                              <CheckCircle className="w-4 h-4" />
                              <span>
                                Resolved {new Date(feedback.resolvedAt).toLocaleDateString()}
                              </span>
                            </div>
                          )}
                        </div>

                        {feedback.adminNotes && (
                          <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded border-l-4 border-blue-400">
                            <p className="text-sm font-medium text-blue-900 dark:text-blue-400 mb-1">
                              Admin Notes:
                            </p>
                            <p className="text-sm text-blue-800 dark:text-blue-300">
                              {feedback.adminNotes}
                            </p>
                          </div>
                        )}
                      </div>

                      <div className="flex items-center space-x-2 ml-4">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setEditingFeedback(feedback.id)}
                        >
                          <Edit3 className="w-4 h-4" />
                        </Button>

                        <Form method="post" className="inline">
                          <input type="hidden" name="action" value="delete" />
                          <input type="hidden" name="feedbackId" value={feedback.id} />
                          <Button
                            type="submit"
                            variant="destructive"
                            size="sm"
                            disabled={isSubmitting}
                            onClick={(e) => {
                              if (!confirm("Are you sure you want to delete this feedback?")) {
                                e.preventDefault();
                              }
                            }}
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </Form>
                      </div>
                    </div>

                    {/* Edit Form */}
                    {editingFeedback === feedback.id && (
                      <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-700 rounded border">
                        <Form method="post" className="space-y-4">
                          <input type="hidden" name="action" value="update" />
                          <input type="hidden" name="feedbackId" value={feedback.id} />

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <Label htmlFor="status">Status</Label>
                              <Select name="status" defaultValue={feedback.status}>
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  {statusOptions.map((status) => (
                                    <SelectItem key={status.value} value={status.value}>
                                      {status.label}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>

                            <div>
                              <Label htmlFor="priority">Priority</Label>
                              <Select name="priority" defaultValue={feedback.priority}>
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  {priorityLevels.map((priority) => (
                                    <SelectItem key={priority.value} value={priority.value}>
                                      {priority.label}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                          </div>

                          <div>
                            <Label htmlFor="assignedTo">Assigned To</Label>
                            <Input
                              id="assignedTo"
                              name="assignedTo"
                              placeholder="Admin username or email"
                              defaultValue={feedback.assignedTo || ""}
                            />
                          </div>

                          <div>
                            <Label htmlFor="adminNotes">Admin Notes</Label>
                            <Textarea
                              id="adminNotes"
                              name="adminNotes"
                              placeholder="Add notes or response for the user..."
                              rows={3}
                              defaultValue={feedback.adminNotes || ""}
                            />
                          </div>

                          <div className="flex space-x-3">
                            <Button type="submit" disabled={isSubmitting}>
                              {isSubmitting ? "Updating..." : "Update Feedback"}
                            </Button>
                            <Button
                              type="button"
                              variant="outline"
                              onClick={() => setEditingFeedback(null)}
                            >
                              Cancel
                            </Button>
                          </div>
                        </Form>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}

            {/* Pagination */}
            {data.pagination.totalPages > 1 && (
              <div className="flex justify-center mt-6">
                <div className="flex items-center space-x-2">
                  <Button variant="outline" size="sm" disabled={!data.pagination.hasPrev}>
                    Previous
                  </Button>
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    Page {data.pagination.currentPage} of {data.pagination.totalPages}
                  </span>
                  <Button variant="outline" size="sm" disabled={!data.pagination.hasNext}>
                    Next
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

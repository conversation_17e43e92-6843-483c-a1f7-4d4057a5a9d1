<svg width="48" height="48" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="dbGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981"/>
      <stop offset="100%" style="stop-color:#059669"/>
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="24" cy="24" r="20" fill="url(#dbGradient)" opacity="0.1"/>
  
  <!-- Database cylinders -->
  <ellipse cx="24" cy="16" rx="12" ry="4" fill="url(#dbGradient)" opacity="0.8"/>
  <rect x="12" y="16" width="24" height="8" fill="url(#dbGradient)" opacity="0.6"/>
  <ellipse cx="24" cy="24" rx="12" ry="4" fill="url(#dbGradient)" opacity="0.8"/>
  <rect x="12" y="24" width="24" height="8" fill="url(#dbGradient)" opacity="0.6"/>
  <ellipse cx="24" cy="32" rx="12" ry="4" fill="url(#dbGradient)" opacity="0.8"/>
  
  <!-- Data flow indicators -->
  <circle cx="38" cy="18" r="2" fill="#10b981">
    <animate attributeName="opacity" values="0.3;1;0.3" dur="2s" repeatCount="indefinite"/>
  </circle>
  <circle cx="38" cy="24" r="2" fill="#059669">
    <animate attributeName="opacity" values="0.3;1;0.3" dur="2s" begin="0.5s" repeatCount="indefinite"/>
  </circle>
  <circle cx="38" cy="30" r="2" fill="#10b981">
    <animate attributeName="opacity" values="0.3;1;0.3" dur="2s" begin="1s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Connection lines -->
  <line x1="36" y1="18" x2="38" y2="18" stroke="#10b981" stroke-width="1"/>
  <line x1="36" y1="24" x2="38" y2="24" stroke="#059669" stroke-width="1"/>
  <line x1="36" y1="30" x2="38" y2="30" stroke="#10b981" stroke-width="1"/>
</svg>

# Task Issue Template

## Issue Title
[Provide a clear and concise title that describes the task]

## Description
### Current State
[Describe the current state of the feature/component]

### Desired State
[Describe what needs to be implemented or changed]

### Requirements Clarification
- [ ] **MANDATORY**: Ensure the description is crystal clear before proceeding
- [ ] If any requirements are unclear, clarify them first before implementation
- [ ] All acceptance criteria are well-defined and measurable

## Architecture Alignment
- [ ] **MANDATORY**: Confirm the proposed changes align with existing architecture
- [ ] Review current codebase structure and patterns
- [ ] Ensure no architectural inconsistencies will be introduced
- [ ] Document any architectural decisions or changes

## Technical Stack Compliance
**The following technical stack is FIXED and must be used:**
- [ ] React (UI framework)
- [ ] Remix (Full-stack framework)
- [ ] Neon (Database)
- [ ] Cloudflare (Hosting/Edge)
- [ ] R2 (Object storage)
- [ ] Biome (Linting/Formatting)
- [ ] TypeScript (Language)
- [ ] Yarn (Package manager)

## Implementation Checklist

### Pre-Development
- [ ] Understand the existing codebase structure
- [ ] Identify files that need to be modified/created
- [ ] Plan the implementation approach

### Development
- [ ] Follow existing code patterns and conventions
- [ ] Maintain TypeScript strict typing
- [ ] Use existing UI components where possible
- [ ] Follow Remix routing and data loading patterns

### Pre-Commit Checklist
- [ ] **MANDATORY**: Run `yarn format` to check code formatting
- [ ] **MANDATORY**: Run `yarn lint` to check for linting issues
- [ ] **MANDATORY**: Run `yarn typecheck` to ensure TypeScript compilation
- [ ] **MANDATORY**: Run `yarn test` to ensure all tests pass
- [ ] **MANDATORY**: Test the CI pipeline locally using `.github/workflows/ci.yml`

### CI/CD Validation
- [ ] **MANDATORY**: Ensure CI pipeline passes before creating PR
- [ ] Verify all GitHub Actions in `ci.yml` complete successfully
- [ ] Check build process completes without errors
- [ ] Validate deployment readiness

### Code Quality Standards
- [ ] Code follows existing patterns and conventions
- [ ] Proper error handling implemented
- [ ] Performance considerations addressed
- [ ] Security best practices followed
- [ ] Accessibility requirements met (if applicable)

## Testing Requirements
- [ ] Unit tests written for new functionality
- [ ] Integration tests updated if needed
- [ ] Manual testing completed
- [ ] Edge cases considered and tested

## Documentation
- [ ] Code is properly commented
- [ ] README updated if necessary
- [ ] API documentation updated (if applicable)
- [ ] Migration guides provided (if breaking changes)

## Acceptance Criteria
[List specific, measurable criteria that must be met for this task to be considered complete]

1. 
2. 
3. 

## Additional Notes
[Any additional context, constraints, or considerations]

---

## Pre-PR Validation Commands
Before creating a Pull Request, run these commands in order:

```bash
# Format code
yarn format

# Check linting
yarn lint

# Type checking
yarn typecheck

# Run tests
yarn test

# Build to ensure compilation works
yarn build
```

## CI Pipeline Verification
Ensure the GitHub Actions workflow (`.github/workflows/ci.yml`) passes locally before submitting PR.

---

**Remember**: Quality over speed. It's better to take time to ensure everything is correct than to rush and introduce bugs or architectural inconsistencies.
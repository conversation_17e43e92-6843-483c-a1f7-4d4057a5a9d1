export interface BlogPost {
  slug: string;
  title: string;
  description: string;
  content: string;
  author: {
    name: string;
    avatar?: string;
    bio?: string;
  };
  publishedAt: string;
  updatedAt?: string;
  tags: string[];
  category: string;
  featured?: boolean;
  readingTime: number;
  coverImage?: string;
}

export interface BlogCategory {
  slug: string;
  name: string;
  description: string;
  count: number;
}

// Mock blog data for demonstration
export const mockBlogPosts: BlogPost[] = [
  {
    slug: "getting-started-with-ai-tools",
    title: "Getting Started with AI Tools: A Complete Guide",
    description:
      "Learn how to leverage AI tools to boost your productivity and create amazing content with our comprehensive starter guide.",
    content: `# Getting Started with AI Tools

AI tools are revolutionizing how we work and create content. In this comprehensive guide, we'll explore the fundamentals of AI tools and how you can start using them today.

## What are AI Tools?

AI tools are software applications that use artificial intelligence to automate tasks, generate content, and provide intelligent insights. They can help with:

- **Content Creation**: Generate text, images, and videos
- **Data Analysis**: Process and analyze large datasets
- **Automation**: Automate repetitive tasks
- **Decision Making**: Provide intelligent recommendations

## Popular AI Tools Categories

### Text Generation
- GPT models for writing and content creation
- Code generation tools for developers
- Translation and language processing

### Image Generation
- DALL-E for creating images from text
- Midjourney for artistic image creation
- Stable Diffusion for open-source image generation

### Data Analysis
- Machine learning platforms
- Predictive analytics tools
- Business intelligence solutions

## Getting Started

1. **Identify Your Needs**: Determine what tasks you want to automate or improve
2. **Choose the Right Tools**: Select AI tools that match your requirements
3. **Start Small**: Begin with simple tasks and gradually increase complexity
4. **Learn and Iterate**: Continuously improve your prompts and workflows

## Best Practices

- Always review AI-generated content
- Understand the limitations of each tool
- Keep human oversight in the loop
- Stay updated with the latest developments

## Conclusion

AI tools are powerful allies in our digital toolkit. By understanding their capabilities and limitations, you can harness their power to enhance your productivity and creativity.`,
    author: {
      name: "Alex Chen",
      avatar:
        "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
      bio: "AI researcher and developer with 5+ years of experience in machine learning.",
    },
    publishedAt: "2024-01-15",
    tags: ["AI", "Getting Started", "Productivity"],
    category: "Tutorial",
    featured: true,
    readingTime: 8,
    coverImage: "https://images.unsplash.com/photo-1677442136019-21780ecad995?w=800&h=400&fit=crop",
  },
  {
    slug: "building-saas-with-remix-cloudflare",
    title: "Building Modern SaaS Applications with Remix and Cloudflare",
    description:
      "Discover how to build scalable SaaS applications using Remix framework and Cloudflare's edge computing platform.",
    content: `# Building Modern SaaS Applications with Remix and Cloudflare

The combination of Remix and Cloudflare provides a powerful foundation for building modern SaaS applications that are fast, scalable, and globally distributed.

## Why Remix + Cloudflare?

### Performance Benefits
- **Edge Computing**: Deploy your application to Cloudflare's global edge network
- **Zero Cold Starts**: Cloudflare Workers eliminate cold start delays
- **Optimized Routing**: Remix's nested routing provides excellent performance

### Developer Experience
- **Full-Stack Framework**: Remix handles both frontend and backend
- **TypeScript Support**: End-to-end type safety
- **Modern Web Standards**: Built on web fundamentals

## Architecture Overview

\`\`\`
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   User Browser  │───▶│ Cloudflare Edge │───▶│  Remix App      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                       │
                                ▼                       ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │   CDN Assets    │    │   Database      │
                       └─────────────────┘    └─────────────────┘
\`\`\`

## Key Features

### 1. Server-Side Rendering
Remix provides excellent SEO and performance with server-side rendering out of the box.

### 2. Progressive Enhancement
Applications work without JavaScript and enhance with it.

### 3. Nested Routing
Organize your application with intuitive nested routes.

### 4. Data Loading
Efficient data loading with loaders and actions.

## Getting Started

1. **Setup**: Initialize your Remix project with Cloudflare template
2. **Database**: Connect to your preferred database (Neon, PlanetScale, etc.)
3. **Authentication**: Implement user authentication
4. **Deployment**: Deploy to Cloudflare Workers

## Best Practices

- Use Cloudflare KV for caching
- Implement proper error boundaries
- Optimize for Core Web Vitals
- Use TypeScript for better DX

This stack provides an excellent foundation for building modern SaaS applications that scale globally.`,
    author: {
      name: "Sarah Johnson",
      avatar:
        "https://images.unsplash.com/photo-*************-2616b612b786?w=150&h=150&fit=crop&crop=face",
      bio: "Full-stack developer specializing in modern web technologies and SaaS architecture.",
    },
    publishedAt: "2024-01-10",
    tags: ["Remix", "Cloudflare", "SaaS", "Web Development"],
    category: "Development",
    readingTime: 12,
    coverImage: "https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=800&h=400&fit=crop",
  },
  {
    slug: "ai-image-generation-best-practices",
    title: "AI Image Generation: Tips and Best Practices",
    description:
      "Master the art of AI image generation with proven techniques, prompt engineering tips, and creative workflows.",
    content: `# AI Image Generation: Tips and Best Practices

AI image generation has opened up incredible possibilities for creators, designers, and businesses. Here's how to get the most out of these powerful tools.

## Understanding AI Image Generation

AI image generation uses machine learning models trained on millions of images to create new visuals based on text descriptions (prompts).

### Popular Tools
- **DALL-E 3**: OpenAI's latest image generation model
- **Midjourney**: Known for artistic and creative outputs
- **Stable Diffusion**: Open-source and highly customizable

## Prompt Engineering

### Basic Structure
\`\`\`
[Subject] + [Style] + [Composition] + [Lighting] + [Details]
\`\`\`

### Example Prompts
- "A futuristic cityscape at sunset, cyberpunk style, wide angle view, neon lighting, highly detailed"
- "Portrait of a wise old wizard, oil painting style, dramatic lighting, fantasy art"

## Best Practices

### 1. Be Specific
- Use descriptive adjectives
- Specify art styles and techniques
- Include composition details

### 2. Iterate and Refine
- Start with basic prompts
- Add details gradually
- Test different variations

### 3. Understand Limitations
- AI may struggle with text in images
- Complex scenes can be challenging
- Results may vary between runs

## Creative Workflows

### 1. Concept Development
- Start with rough ideas
- Generate multiple variations
- Refine based on results

### 2. Style Exploration
- Try different artistic styles
- Combine multiple influences
- Experiment with techniques

### 3. Post-Processing
- Use traditional editing tools
- Combine multiple AI generations
- Add final touches manually

## Commercial Considerations

- Check licensing terms
- Understand usage rights
- Consider ethical implications
- Respect copyright and trademarks

## Future Trends

- Improved prompt understanding
- Better consistency across generations
- Integration with design tools
- Real-time generation capabilities

AI image generation is a powerful creative tool when used thoughtfully and skillfully.`,
    author: {
      name: "Mike Rodriguez",
      avatar:
        "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",
      bio: "Digital artist and AI enthusiast exploring the intersection of technology and creativity.",
    },
    publishedAt: "2024-01-05",
    tags: ["AI", "Image Generation", "Creative", "Design"],
    category: "Creative",
    readingTime: 10,
    coverImage: "https://images.unsplash.com/photo-1547036967-23d11aacaee0?w=800&h=400&fit=crop",
  },
];

export const mockCategories: BlogCategory[] = [
  {
    slug: "tutorial",
    name: "Tutorial",
    description: "Step-by-step guides and tutorials",
    count: 1,
  },
  {
    slug: "development",
    name: "Development",
    description: "Web development and programming topics",
    count: 1,
  },
  {
    slug: "creative",
    name: "Creative",
    description: "Creative tools and techniques",
    count: 1,
  },
  {
    slug: "ai",
    name: "AI & Machine Learning",
    description: "Artificial intelligence and ML topics",
    count: 3,
  },
];

export function getBlogPosts(): BlogPost[] {
  return mockBlogPosts.sort(
    (a, b) => new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime()
  );
}

export function getBlogPost(slug: string): BlogPost | null {
  return mockBlogPosts.find((post) => post.slug === slug) || null;
}

export function getBlogCategories(): BlogCategory[] {
  return mockCategories;
}

export function getBlogPostsByCategory(category: string): BlogPost[] {
  return mockBlogPosts.filter((post) => post.category.toLowerCase() === category.toLowerCase());
}

export function getBlogPostsByTag(tag: string): BlogPost[] {
  return mockBlogPosts.filter((post) =>
    post.tags.some((t) => t.toLowerCase() === tag.toLowerCase())
  );
}

export function getFeaturedPosts(): BlogPost[] {
  return mockBlogPosts.filter((post) => post.featured);
}

export function searchBlogPosts(query: string): BlogPost[] {
  const lowercaseQuery = query.toLowerCase();
  return mockBlogPosts.filter(
    (post) =>
      post.title.toLowerCase().includes(lowercaseQuery) ||
      post.description.toLowerCase().includes(lowercaseQuery) ||
      post.tags.some((tag) => tag.toLowerCase().includes(lowercaseQuery))
  );
}

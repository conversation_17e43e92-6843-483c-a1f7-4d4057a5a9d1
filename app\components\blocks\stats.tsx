import CardGrid from "./card-grid";
import ContentSection from "./content-section";

interface StatItem {
  label: string;
  value: string;
  description: string;
}

interface StatsProps {
  title: string;
  description: string;
  items: StatItem[];
}

export default function Stats({ title, description, items }: StatsProps) {
  // Transform stats to CardItem format
  const cardItems = items.map((item) => ({
    title: item.value,
    description: item.description,
    label: item.label,
    value: item.value,
  }));

  return (
    <ContentSection
      title={title}
      description={description}
      background="stats"
      decorations={true}
      padding="md"
      headerSpacing="md"
    >
      <CardGrid items={cardItems} columns={3} variant="stats" animationDelay={0.2} />
    </ContentSection>
  );
}

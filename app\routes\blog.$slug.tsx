import type { LoaderFunctionArgs, MetaFunction } from "@remix-run/cloudflare";
import { json } from "@remix-run/cloudflare";
import { Link, useLoaderData } from "@remix-run/react";
import { ArrowLeft, BookOpen, Calendar, Clock, Share2, User } from "lucide-react";

import { DocumentRenderer } from "@keystatic/core/renderer";
import UnifiedLayout from "~/components/layout/unified-layout";
import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import {
  type BlogPost,
  formatDate,
  getAllPosts,
  getPost,
  getReadingTime,
} from "~/lib/content/keystatic";

export const meta: MetaFunction<typeof loader> = ({ data }) => {
  if (!data?.post) {
    return [
      { title: "Post Not Found - AI SaaS Starter" },
      { name: "description", content: "The requested blog post could not be found." },
    ];
  }

  const { post } = data;
  return [
    { title: `${post.seo?.title || post.title} - AI SaaS Starter` },
    { name: "description", content: post.seo?.description || post.excerpt },
    { name: "keywords", content: post.seo?.keywords?.join(", ") || post.tags.join(", ") },
    { property: "og:title", content: post.seo?.title || post.title },
    { property: "og:description", content: post.seo?.description || post.excerpt },
    { property: "og:image", content: post.featuredImage || "/images/default-og.jpg" },
    { property: "og:type", content: "article" },
    { property: "article:author", content: post.author },
    { property: "article:published_time", content: post.publishedDate },
    { property: "article:tag", content: post.tags.join(", ") },
  ];
};

export async function loader({ params }: LoaderFunctionArgs) {
  const { slug } = params;

  if (!slug) {
    throw new Response("Not Found", { status: 404 });
  }

  try {
    const post = await getPost(slug);

    if (!post || post.status !== "published") {
      throw new Response("Not Found", { status: 404 });
    }

    // Get related posts (same category, excluding current post)
    const allPosts = await getAllPosts();
    const relatedPosts = allPosts
      .filter((p) => p.category === post.category && p.slug !== post.slug)
      .slice(0, 3);

    return json({ post, relatedPosts });
  } catch (error) {
    console.error(`Error loading post ${slug}:`, error);
    throw new Response("Not Found", { status: 404 });
  }
}

function RelatedPostCard({ post }: { post: BlogPost }) {
  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-center gap-2 text-xs text-muted-foreground mb-2">
          <Calendar className="h-3 w-3" />
          {formatDate(post.publishedDate)}
          <Clock className="h-3 w-3 ml-2" />
          {getReadingTime(post.content)} min
        </div>
        <CardTitle className="text-base line-clamp-2">
          <Link to={`/blog/${post.slug}`} className="hover:text-primary transition-colors">
            {post.title}
          </Link>
        </CardTitle>
        <CardDescription className="text-sm line-clamp-2">{post.excerpt}</CardDescription>
      </CardHeader>
    </Card>
  );
}

export default function BlogPost() {
  const { post, relatedPosts } = useLoaderData<typeof loader>();

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: post.title,
          text: post.description,
          url: window.location.href,
        });
      } catch (error) {
        console.log("Error sharing:", error);
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href);
    }
  };

  return (
    <UnifiedLayout
      hero={{
        title: post.title,
        description: `By ${post.author} • ${formatDate(post.publishedDate)} • ${getReadingTime(post.content)} min read`,
      }}
    >
      <article className="py-16">
        <div className="max-w-6xl mx-auto px-4">
          {/* Back Button */}
          <div className="mb-8">
            <Button variant="ghost" asChild>
              <Link to="/blog" className="flex items-center gap-2">
                <ArrowLeft className="h-4 w-4" />
                Back to Blog
              </Link>
            </Button>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-3">
              {/* Header */}
              <header className="mb-8">
                {post.featuredImage && (
                  <div className="aspect-video mb-8 overflow-hidden rounded-lg">
                    <img
                      src={post.featuredImage}
                      alt={post.title}
                      className="w-full h-full object-cover"
                    />
                  </div>
                )}

                <div className="space-y-4">
                  <div className="flex flex-wrap items-center gap-2">
                    <Badge variant="secondary">
                      {post.category.replace("-", " ").replace(/\b\w/g, (l) => l.toUpperCase())}
                    </Badge>
                  </div>

                  <h1 className="text-4xl font-bold leading-tight lg:text-5xl">{post.title}</h1>

                  {post.excerpt && <p className="text-xl text-muted-foreground">{post.excerpt}</p>}

                  <div className="flex items-center justify-between pt-4 border-t">
                    <div className="flex items-center gap-4">
                      <Avatar>
                        <AvatarFallback>
                          {post.author
                            .split(" ")
                            .map((n) => n[0])
                            .join("")}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-semibold">{post.author}</p>
                        <div className="flex items-center gap-4 text-sm text-muted-foreground">
                          <span className="flex items-center gap-1">
                            <Calendar className="h-4 w-4" />
                            {formatDate(post.publishedDate)}
                          </span>
                          <span className="flex items-center gap-1">
                            <Clock className="h-4 w-4" />
                            {getReadingTime(post.content)} min read
                          </span>
                        </div>
                      </div>
                    </div>

                    <Button variant="outline" size="sm" onClick={handleShare}>
                      <Share2 className="h-4 w-4 mr-2" />
                      Share
                    </Button>
                  </div>
                </div>
              </header>

              {/* Content */}
              <div className="prose prose-lg max-w-none prose-headings:font-bold prose-h1:text-3xl prose-h2:text-2xl prose-h3:text-xl prose-p:text-muted-foreground prose-a:text-primary prose-a:no-underline hover:prose-a:underline prose-strong:text-foreground prose-code:bg-muted prose-code:px-1 prose-code:py-0.5 prose-code:rounded prose-pre:bg-muted prose-pre:border">
                <DocumentRenderer document={post.content} />
              </div>

              {/* Tags */}
              <div className="mt-12 pt-8 border-t">
                <h3 className="text-lg font-semibold mb-4">Tags</h3>
                <div className="flex flex-wrap gap-2">
                  {post.tags.map((tag) => (
                    <Link
                      key={tag}
                      to={`/blog?q=${encodeURIComponent(tag)}`}
                      className="px-3 py-1 bg-muted rounded-full text-sm hover:bg-muted/80 transition-colors"
                    >
                      #{tag}
                    </Link>
                  ))}
                </div>
              </div>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <div className="sticky top-24 space-y-6">
                {/* Table of Contents */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-base">
                      <BookOpen className="h-4 w-4" />
                      Table of Contents
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-sm text-muted-foreground">
                      <p>Navigate through the article sections</p>
                    </div>
                  </CardContent>
                </Card>

                {/* Related Posts */}
                {relatedPosts.length > 0 && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base">Related Articles</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {relatedPosts.map((relatedPost) => (
                          <RelatedPostCard key={relatedPost.slug} post={relatedPost} />
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Newsletter */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Stay Updated</CardTitle>
                    <CardDescription>
                      Get the latest articles delivered to your inbox.
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button className="w-full" variant="outline">
                      Subscribe to Newsletter
                    </Button>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </article>
    </UnifiedLayout>
  );
}

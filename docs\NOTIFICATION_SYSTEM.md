# Notification System

## Overview

The Notification System provides comprehensive in-app and email notification functionality, supporting real-time user communication, system alerts, and automated notifications for various events. It's designed to be flexible, scalable, and user-friendly with support for multiple notification types and delivery channels.

## Features

### ✅ Implemented Features

#### 1. **In-App Notifications**
- **Real-time Display**: Notifications appear instantly in the application
- **Notification Bell**: Header component with unread count badge
- **Notification Center**: Dedicated page for managing all notifications
- **Interactive Actions**: Mark as read, dismiss, bulk operations
- **Filtering & Pagination**: Filter by type, status, and paginated display

#### 2. **Email Notifications**
- **Template System**: Flexible email template engine
- **Multiple Templates**: General notifications, payment success, welcome emails
- **HTML & Text**: Both HTML and plain text email formats
- **Variable Substitution**: Dynamic content insertion
- **Email Service Integration**: Seamless email delivery

#### 3. **Notification Types**
- **Info**: General information and updates
- **Success**: Successful operations and confirmations
- **Warning**: Important warnings and alerts
- **Error**: Error messages and failures
- **Payment**: Payment-related notifications
- **Credit**: Credit balance and usage notifications
- **Security**: Security alerts and warnings
- **System**: System updates and maintenance

#### 4. **Delivery Channels**
- **In-App Only**: Notifications displayed only within the application
- **Email Only**: Notifications sent only via email
- **Both Channels**: Notifications delivered through both in-app and email

#### 5. **Management Features**
- **Notification Preferences**: User-configurable notification settings
- **Bulk Operations**: Mark all as read, dismiss multiple notifications
- **Expiration Handling**: Automatic cleanup of expired notifications
- **Unread Tracking**: Real-time unread count updates

## File Structure

```
app/
├── routes/
│   ├── console.notifications.tsx        # Main notification management page
│   ├── api.notifications.tsx            # Notification API endpoints
│   └── test.notification-system.tsx     # Development test page
├── services/
│   ├── notification.server.ts           # Core notification service
│   └── notification-helpers.server.ts   # Helper functions for common notifications
├── components/
│   └── notifications/
│       └── notification-bell.tsx        # Notification bell component
├── lib/
│   └── email/
│       └── templates/
│           ├── notification.ts          # General notification template
│           ├── payment-success.ts       # Payment success template
│           └── index.ts                 # Template registry
└── docs/
    └── NOTIFICATION_SYSTEM.md          # This documentation
```

## Database Schema

### Notifications Table
```sql
CREATE TABLE notifications (
  id SERIAL PRIMARY KEY,
  account_id TEXT NOT NULL REFERENCES accounts(id),
  user_id TEXT REFERENCES users(id),
  title TEXT NOT NULL,
  body TEXT NOT NULL,
  type notification_type NOT NULL,
  channel TEXT NOT NULL, -- 'in_app', 'email', 'both'
  link TEXT,
  dismissed BOOLEAN DEFAULT FALSE,
  read_at TIMESTAMP,
  expires_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Notification types enum
CREATE TYPE notification_type AS ENUM (
  'info', 'warning', 'error', 'success',
  'payment', 'credit', 'usage', 'system', 'security'
);
```

### Indexes for Performance
```sql
CREATE INDEX idx_notifications_account_id ON notifications(account_id);
CREATE INDEX idx_notifications_read_at ON notifications(read_at);
CREATE INDEX idx_notifications_dismissed ON notifications(dismissed);
CREATE INDEX idx_notifications_type ON notifications(type);
CREATE INDEX idx_notifications_created_at ON notifications(created_at);
```

## API Endpoints

### Notification Management
```typescript
// Get user notifications
GET /api/notifications?action=list&page=1&limit=20&unread=true&type=payment
Response: {
  success: true,
  data: {
    notifications: UserNotification[],
    pagination: PaginationInfo
  }
}

// Get unread count
GET /api/notifications?action=unread-count
Response: {
  success: true,
  data: { unreadCount: number }
}

// Create notification
POST /api/notifications
Body: {
  action: "create",
  title: string,
  body: string,
  type: NotificationType,
  channel: NotificationChannel,
  link?: string,
  expiresAt?: string,
  emailTemplate?: string,
  emailVariables?: object
}

// Mark as read
POST /api/notifications
Body: {
  action: "mark-read",
  notificationId: number
}

// Dismiss notification
POST /api/notifications
Body: {
  action: "dismiss",
  notificationId: number
}

// Mark all as read
POST /api/notifications
Body: {
  action: "mark-all-read"
}
```

### Console Page Actions
```typescript
// Notification management page
GET /console/notifications?page=1&unread=true&type=payment
POST /console/notifications
FormData: {
  action: "mark-read" | "dismiss" | "mark-all-read",
  notificationId?: string
}
```

## Core Services

### Notification Service (`notification.server.ts`)

#### Main Functions
```typescript
// Create notification
createNotification(params: CreateNotificationParams, db: Database)
// Returns: { success: boolean, notificationId?: number, error?: string }

// Get user notifications with pagination
getUserNotifications(accountId: string, options: GetNotificationsOptions, db: Database)
// Returns: { notifications: UserNotification[], pagination: PaginationInfo }

// Mark notification as read
markNotificationAsRead(notificationId: number, accountId: string, db: Database)
// Returns: { success: boolean, error?: string }

// Dismiss notification
dismissNotification(notificationId: number, accountId: string, db: Database)
// Returns: { success: boolean, error?: string }

// Mark all notifications as read
markAllNotificationsAsRead(accountId: string, db: Database)
// Returns: { success: boolean, error?: string }

// Get unread count
getUnreadNotificationCount(accountId: string, db: Database)
// Returns: number
```

### Notification Helpers (`notification-helpers.server.ts`)

#### Helper Functions for Common Notifications
```typescript
// Payment success notification
notifyPaymentSuccess(userUuid: string, params: PaymentParams, db: Database)

// Payment failed notification
notifyPaymentFailed(userUuid: string, params: PaymentFailedParams, db: Database)

// Credits added notification
notifyCreditsAdded(userUuid: string, params: CreditsParams, db: Database)

// Low credits warning
notifyLowCredits(userUuid: string, params: LowCreditsParams, db: Database)

// Subscription cancelled notification
notifySubscriptionCancelled(userUuid: string, params: SubscriptionParams, db: Database)

// Security alert notification
notifySecurityAlert(userUuid: string, params: SecurityParams, db: Database)

// System maintenance notification
notifySystemMaintenance(userUuid: string, params: MaintenanceParams, db: Database)

// Welcome notification for new users
notifyWelcome(userUuid: string, params: WelcomeParams, db: Database)

// API usage limit notification
notifyUsageLimit(userUuid: string, params: UsageLimitParams, db: Database)
```

## Email Templates

### Template System
The notification system uses a flexible template engine that supports:
- **Variable Substitution**: Dynamic content insertion
- **HTML & Text Formats**: Both rich HTML and plain text emails
- **Template Registry**: Centralized template management
- **Custom Templates**: Easy addition of new email templates

### Available Templates

#### 1. General Notification Template (`notification.ts`)
- **Purpose**: Generic notification emails for various events
- **Variables**: `name`, `title`, `body`, `type`, `actionUrl`, `actionText`
- **Features**: Type-specific styling, action buttons, unsubscribe links

#### 2. Payment Success Template (`payment-success.ts`)
- **Purpose**: Payment confirmation emails
- **Variables**: `name`, `amount`, `currency`, `planName`, `creditsAdded`, `nextBillingDate`
- **Features**: Payment details, credit highlights, invoice links

### Template Usage
```typescript
// Using general notification template
await createNotification({
  accountId: userUuid,
  title: "Welcome!",
  body: "Your account has been created successfully.",
  type: "success",
  channel: "both",
  emailTemplate: "notification",
  emailVariables: {
    actionUrl: "/console/dashboard",
    actionText: "Get Started"
  }
}, db);

// Using payment success template
await createNotification({
  accountId: userUuid,
  title: "Payment Successful",
  body: "Your payment has been processed.",
  type: "payment",
  channel: "both",
  emailTemplate: "payment-success",
  emailVariables: {
    amount: "19.99",
    currency: "USD",
    planName: "Pro Plan",
    creditsAdded: 2000
  }
}, db);
```

## User Interface Components

### Notification Bell Component (`notification-bell.tsx`)

#### Features
- **Unread Count Badge**: Visual indicator of unread notifications
- **Dropdown Menu**: Quick access to recent notifications
- **Real-time Updates**: Automatic polling for new notifications
- **Interactive Actions**: Mark as read, dismiss, view details
- **Responsive Design**: Mobile and desktop optimized

#### Usage
```typescript
import NotificationBell from "~/components/notifications/notification-bell";

// In header component
<NotificationBell initialCount={unreadCount} />
```

### Notification Management Page (`console.notifications.tsx`)

#### Features
- **Comprehensive List**: All user notifications with pagination
- **Filtering Options**: Filter by type, read status, date range
- **Bulk Operations**: Mark all as read, bulk dismiss
- **Interactive UI**: Click to read, dismiss, follow links
- **Responsive Layout**: Mobile-friendly design

#### Navigation Integration
- **Console Sidebar**: Direct link to notification center
- **User Menu**: Quick access to notifications
- **Breadcrumb Navigation**: Clear navigation context

## Integration Points

### Stripe Webhook Integration
The notification system is integrated with Stripe webhooks to automatically create notifications for:
- **Payment Success**: When subscription payments are processed
- **Payment Failed**: When payment attempts fail
- **Subscription Changes**: When subscriptions are modified or cancelled

### Credit System Integration
Notifications are automatically created for:
- **Credits Added**: When credits are added to user accounts
- **Low Credit Warnings**: When credit balance falls below threshold
- **Credit Usage**: For significant credit consumption events

### User Action Integration
Notifications are triggered by:
- **Account Creation**: Welcome notifications for new users
- **Security Events**: Login alerts, password changes
- **Subscription Changes**: Plan upgrades, downgrades, cancellations
- **System Events**: Maintenance notifications, feature updates

## Testing

### Test Page
Visit `/test/notification-system` to:
- View implementation status and features
- Access quick navigation to notification pages
- Review notification types and test scenarios
- Get testing instructions and API endpoint documentation

### Test Scenarios

#### 1. Payment Notification Flow
1. Complete a subscription payment via Stripe
2. Verify webhook processing creates notification
3. Check in-app notification display
4. Verify email notification delivery
5. Test notification interaction (read, dismiss)

#### 2. Credit Management Notifications
1. Add credits to user account
2. Verify credit notification creation
3. Test low credit warning triggers
4. Check notification preferences
5. Verify email delivery for credit events

#### 3. Security Alert Testing
1. Trigger security event (login, password change)
2. Verify security notification creation
3. Check email alert delivery
4. Test notification urgency handling
5. Verify proper notification display

#### 4. System Notification Broadcasting
1. Create system maintenance notification
2. Verify broadcast to all users
3. Check notification expiration handling
4. Test notification dismissal
5. Verify cleanup of expired notifications

### Manual Testing Checklist

#### Notification Creation
- [ ] Create notifications via API
- [ ] Verify database storage
- [ ] Check email delivery
- [ ] Test notification display
- [ ] Verify unread count updates

#### Notification Management
- [ ] Mark notifications as read
- [ ] Dismiss notifications
- [ ] Test bulk operations
- [ ] Verify filtering functionality
- [ ] Check pagination

#### Email Templates
- [ ] Test general notification template
- [ ] Verify payment success template
- [ ] Check variable substitution
- [ ] Test HTML and text formats
- [ ] Verify email delivery

#### User Interface
- [ ] Test notification bell component
- [ ] Verify dropdown functionality
- [ ] Check notification center page
- [ ] Test responsive design
- [ ] Verify accessibility

## Performance Considerations

### Database Optimization
- **Indexed Queries**: Efficient notification retrieval
- **Pagination**: Limit query results for better performance
- **Cleanup Jobs**: Regular removal of old notifications
- **Read Optimization**: Optimized unread count queries

### Real-time Updates
- **Polling Strategy**: Efficient polling for new notifications
- **Caching**: Cache unread counts and recent notifications
- **Batch Operations**: Efficient bulk notification operations

### Email Delivery
- **Async Processing**: Non-blocking email sending
- **Template Caching**: Cache compiled email templates
- **Delivery Optimization**: Efficient email service integration

## Security Considerations

### Data Protection
- **User Isolation**: Users can only access their own notifications
- **Input Validation**: Sanitize notification content
- **XSS Prevention**: Escape notification content in UI
- **CSRF Protection**: Secure notification management actions

### Email Security
- **Template Security**: Prevent template injection attacks
- **Link Validation**: Validate notification links
- **Unsubscribe Handling**: Secure unsubscribe mechanisms
- **Spam Prevention**: Rate limiting for notification creation

## Monitoring and Analytics

### Notification Metrics
- **Delivery Rates**: Track notification delivery success
- **Read Rates**: Monitor notification engagement
- **Dismissal Patterns**: Analyze user notification behavior
- **Email Open Rates**: Track email notification effectiveness

### System Health
- **Queue Monitoring**: Monitor notification processing queues
- **Error Tracking**: Track notification creation and delivery errors
- **Performance Metrics**: Monitor notification system performance
- **Database Health**: Monitor notification table growth and cleanup

## Future Enhancements

### Planned Features
- [ ] Push notifications for mobile devices
- [ ] SMS notification support
- [ ] Advanced notification scheduling
- [ ] Notification templates for admins
- [ ] A/B testing for notification content
- [ ] Advanced analytics dashboard
- [ ] Notification automation rules
- [ ] Integration with external services

### Integration Opportunities
- [ ] Slack/Discord integration
- [ ] Webhook notifications for external systems
- [ ] Advanced email marketing integration
- [ ] Mobile app push notification service
- [ ] Real-time WebSocket notifications

## Troubleshooting

### Common Issues
1. **Notifications not appearing**: Check user authentication and database connection
2. **Email not sending**: Verify email service configuration and templates
3. **Unread count incorrect**: Check notification read status updates
4. **Performance issues**: Review database indexes and query optimization

### Debug Tools
- **API Testing**: Use notification API endpoints for debugging
- **Database Queries**: Direct database inspection for notification data
- **Email Logs**: Check email service logs for delivery issues
- **Browser Console**: Check for JavaScript errors in notification components

### Error Handling
- **Graceful Degradation**: System continues to function if notifications fail
- **Error Logging**: Comprehensive error logging for debugging
- **Retry Mechanisms**: Automatic retry for failed email deliveries
- **Fallback Options**: Alternative notification methods if primary fails

/**
 * Hash and ID generation utilities
 */

/**
 * Generate a unique UUID
 */
export function getUuid(): string {
  return crypto.randomUUID();
}

/**
 * Generate a snowflake-like ID (timestamp-based)
 */
export function getSnowId(): string {
  const timestamp = Date.now();
  const random = Math.floor(Math.random() * 1000);
  return `${timestamp}${random.toString().padStart(3, "0")}`;
}

/**
 * Generate a random string of specified length
 */
export function generateRandomString(length = 8): string {
  const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
  let result = "";
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * Generate a random invite code
 */
export function generateInviteCode(): string {
  return generateRandomString(6).toUpperCase();
}

// types/global.d.ts
declare module "google-one-tap" {
  interface IdConfiguration {
    client_id: string;
    auto_select?: boolean;
    callback?: (response: CredentialResponse) => void;
    login_uri?: string;
    native_callback?: (response: CredentialResponse) => void;
    cancel_on_tap_outside?: boolean;
    prompt_parent_id?: string;
    nonce?: string;
    context?: string;
    state_cookie_domain?: string;
    ux_mode?: "popup" | "redirect";
    allowed_parent_origin?: string | string[];
    intermediate_iframe_close_callback?: () => void;
    itp_support?: boolean;
    login_hint?: string;
    hd?: string;
    use_fedcm_for_prompt?: boolean;
  }

  interface CredentialResponse {
    credential?: string; // This is the ID token
    select_by?: "auto" | "user" | "user_1tap" | "user_2tap";
    clientId?: string;
    gsiwebsdk?: "load" | "render" | "select";
  }

  global {
    interface Window {
      google?: {
        accounts?: {
          id?: {
            initialize: (config: IdConfiguration) => void;
            prompt: (momentNotification?: (notification: PromptMomentNotification) => void) => void;
            renderButton: (
              parent: HTMLElement,
              options: {
                theme?: string;
                size?: string;
                text?: string;
                width?: string;
                type?: string;
                shape?: string;
                logo_alignment?: string;
              },
              callback?: (response: CredentialResponse) => void
            ) => void;
            disableAutoSelect: () => void;
            storeCredential: (credential: string, callback?: () => void) => void;
            cancel: () => void;
            revoke: (id_token: string, callback?: () => void) => void;
          };
          oauth2?: {
            initTokenClient: (config: TokenClientConfig) => TokenClient;
            initCodeClient: (config: CodeClientConfig) => CodeClient;
            hasGrantedAllScopes: (tokenResponse: TokenResponse, ...scopes: string[]) => boolean;
            hasGrantedAnyScope: (tokenResponse: TokenResponse, ...scopes: string[]) => boolean;
            revoke: (accessToken: string, done: () => void) => void;
            googleAccountsApiLoaded: () => void; // Custom addition, may not be standard
          };
        };
      };
    }
  }

  // Define types for OAuth2 client if needed, based on GIS library
  interface TokenClientConfig {
    client_id: string;
    callback: (tokenResponse: TokenResponse) => void;
    scope: string;
    prompt?: "" | "none" | "consent" | "select_account";
    hint?: string;
    error_callback?: (error: Error) => void;
  }

  interface TokenResponse {
    access_token: string;
    expires_in: number;
    scope: string;
    token_type: string;
    // Potentially other fields like id_token, refresh_token
  }

  interface CodeClientConfig {
    client_id: string;
    callback: (codeResponse: CodeResponse) => void;
    scope: string;
    prompt?: "" | "none" | "consent" | "select_account";
    hint?: string;
    error_callback?: (error: Error) => void;
    ux_mode?: "popup" | "redirect";
    state?: string;
  }

  interface CodeResponse {
    code: string;
    scope: string;
    // Potentially other fields
  }

  interface TokenClient {
    requestAccessToken: (overrideConfig?: {
      prompt?: string;
      hint?: string;
      scope?: string;
    }) => void;
  }

  interface CodeClient {
    requestCode: () => void;
  }

  interface PromptMomentNotification {
    isDisplayed: () => boolean;
    isNotDisplayed: () => boolean;
    isSkippedMoment: () => boolean;
    getNotDisplayedReason: () => string; // e.g., 'browser_not_supported', 'missing_client_id', etc.
    getSkippedReason: () => string; // e.g., 'tap_outside', 'user_cancel', etc.
    getDismissedReason: () => string; // e.g., 'credential_returned', 'cancel_called', etc.
  }
}

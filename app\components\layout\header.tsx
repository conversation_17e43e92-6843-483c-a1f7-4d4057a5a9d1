import { Link } from "@remix-run/react";
import {
  Bell,
  BookOpen,
  Bot,
  ChevronDown,
  Code,
  CreditCard,
  Edit3,
  LogOut,
  Menu,
  MessageSquare,
  Phone,
  Search,
  Settings,
  Shield,
  Sparkles,
  User,
  Users,
} from "lucide-react";
import { useState } from "react";

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "~/components/ui/accordion";
import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import { Badge } from "~/components/ui/badge";
import { Button, buttonVariants } from "~/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import { Input } from "~/components/ui/input";
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  navigationMenuTriggerStyle,
} from "~/components/ui/navigation-menu";
import { She<PERSON>, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from "~/components/ui/sheet";
import { cn } from "~/lib/utils/utils";

export interface NavItem {
  title: string;
  url: string;
  description?: string;
  icon?: React.ReactNode;
  children?: NavItem[];
  target?: string;
}

export interface SearchProps {
  placeholder?: string;
  onSearch?: (query: string) => void;
  showSearchButton?: boolean;
  className?: string;
}

export interface UserAccountProps {
  user?: {
    name: string;
    email: string;
    avatar?: string;
    initials?: string;
  };
  onSignIn?: () => void;
  onSignOut?: () => void;
  showNotifications?: boolean;
  notificationCount?: number;
}

export interface TrustSignalsProps {
  phone?: string;
  certifications?: string[];
  securityBadge?: boolean;
}

export interface HeaderProps {
  brand?: {
    title: string;
    url: string;
    logo?: string;
  };
  navigation?: NavItem[];
  buttons?: {
    title: string;
    url: string;
    variant?: "default" | "outline" | "secondary" | "ghost" | "link" | "destructive";
    target?: string;
  }[];
  search?: SearchProps;
  userAccount?: UserAccountProps;
  trustSignals?: TrustSignalsProps;
}

const defaultNavigation: NavItem[] = [
  {
    title: "Features",
    url: "#",
    children: [
      {
        title: "AI Tools",
        url: "/dev.ai-tools",
        description: "Comprehensive AI tools for text generation and image creation",
        icon: <MessageSquare className="h-4 w-4" />,
      },
      {
        title: "Cloudflare AI",
        url: "/dev.cloudflare-ai",
        description: "Edge AI computing with Cloudflare Workers AI",
        icon: <Bot className="h-4 w-4" />,
      },
      {
        title: "Components",
        url: "/dev.components",
        description: "Beautiful UI components built with Radix and Tailwind",
        icon: <Sparkles className="h-4 w-4" />,
      },
      {
        title: "Analytics",
        url: "/dev.performance",
        description: "Performance monitoring and analytics dashboard",
        icon: <Settings className="h-4 w-4" />,
      },
    ],
  },
  {
    title: "Developer",
    url: "#",
    children: [
      {
        title: "Developer Center",
        url: "/dev",
        description: "Development tools and test page navigation center",
        icon: <Code className="h-4 w-4" />,
      },
      {
        title: "Development Test",
        url: "/dev-test",
        description: "Unified functional testing page",
        icon: <Settings className="h-4 w-4" />,
      },
      {
        title: "Language Switcher",
        url: "/language-demo",
        description: "Language switcher component showcase",
        icon: <Sparkles className="h-4 w-4" />,
      },
      {
        title: "Content Management",
        url: "/keystatic",
        description: "Manage blog posts, pages, and site content",
        icon: <Edit3 className="h-4 w-4" />,
      },
    ],
  },
  {
    title: "Pricing",
    url: "/pricing",
    icon: <CreditCard className="h-4 w-4" />,
  },
  {
    title: "Blog",
    url: "/blog",
    icon: <BookOpen className="h-4 w-4" />,
  },
  {
    title: "About",
    url: "/about",
    icon: <Users className="h-4 w-4" />,
  },
];

const defaultButtons = [
  {
    title: "Get Started",
    url: "/ai-tools",
    variant: "default" as const,
  },
];

// Search Component
function SearchBar({
  placeholder = "Search...",
  onSearch,
  showSearchButton = true,
  className,
}: SearchProps) {
  const [searchQuery, setSearchQuery] = useState("");

  const handleSearch = () => {
    if (onSearch && searchQuery.trim()) {
      onSearch(searchQuery.trim());
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSearch();
    }
  };

  return (
    <div className={cn("relative flex items-center max-w-sm", className)}>
      <Input
        type="search"
        placeholder={placeholder}
        value={searchQuery}
        onChange={(e) => setSearchQuery(e.target.value)}
        onKeyDown={handleKeyDown}
        className="pr-10 bg-muted/50 border-muted-foreground/20 focus:border-primary"
        aria-label="Search"
      />
      {showSearchButton && (
        <Button
          size="sm"
          variant="ghost"
          onClick={handleSearch}
          className="absolute right-1 h-7 w-7 p-0 hover:bg-primary hover:text-primary-foreground"
          aria-label="Search button"
        >
          <Search className="h-4 w-4" />
        </Button>
      )}
    </div>
  );
}

// User Account Component
function UserAccountMenu({
  user,
  onSignIn,
  onSignOut,
  showNotifications,
  notificationCount,
}: UserAccountProps) {
  if (!user) {
    return (
      <div className="flex items-center gap-2">
        <Button variant="ghost" onClick={onSignIn} className="text-sm">
          Sign In
        </Button>
        <Button onClick={onSignIn} className="text-sm">
          Sign Up
        </Button>
      </div>
    );
  }

  return (
    <div className="flex items-center gap-3">
      {showNotifications && (
        <Button variant="ghost" size="icon" className="relative">
          <Bell className="h-5 w-5" />
          {notificationCount && notificationCount > 0 && (
            <Badge
              variant="destructive"
              className="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 text-xs flex items-center justify-center"
            >
              {notificationCount > 9 ? "9+" : notificationCount}
            </Badge>
          )}
          <span className="sr-only">Notifications</span>
        </Button>
      )}

      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="flex items-center gap-2 hover:bg-muted">
            <Avatar className="h-8 w-8">
              <AvatarImage src={user.avatar} alt={user.name} />
              <AvatarFallback className="bg-primary text-primary-foreground text-sm">
                {user.initials || user.name.charAt(0).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <span className="hidden md:block text-sm font-medium">{user.name}</span>
            <ChevronDown className="h-4 w-4 opacity-50" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-56">
          <DropdownMenuLabel>
            <div className="flex flex-col space-y-1">
              <p className="text-sm font-medium">{user.name}</p>
              <p className="text-xs text-muted-foreground">{user.email}</p>
            </div>
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem asChild>
            <Link to="/profile" className="flex items-center gap-2">
              <User className="h-4 w-4" />
              Profile
            </Link>
          </DropdownMenuItem>
          <DropdownMenuItem asChild>
            <Link to="/settings" className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              Settings
            </Link>
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={onSignOut} className="flex items-center gap-2 text-red-600">
            <LogOut className="h-4 w-4" />
            Sign Out
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}

// Trust Signals Component
function TrustSignals({ phone, certifications, securityBadge }: TrustSignalsProps) {
  if (!phone && !certifications?.length && !securityBadge) return null;

  return (
    <div className="hidden lg:flex items-center gap-4 text-sm text-muted-foreground">
      {phone && (
        <div className="flex items-center gap-1">
          <Phone className="h-4 w-4" />
          <span>{phone}</span>
        </div>
      )}
      {securityBadge && (
        <div className="flex items-center gap-1">
          <Shield className="h-4 w-4 text-green-600" />
          <span className="text-green-600">Secure</span>
        </div>
      )}
      {certifications?.map((cert, i) => (
        <Badge key={`${cert}-${i}`} variant="outline" className="text-xs">
          {cert}
        </Badge>
      ))}
    </div>
  );
}

export default function Header({
  brand = {
    title: "AI SaaS Starter",
    url: "/",
  },
  navigation = defaultNavigation,
  buttons = defaultButtons,
  search,
  userAccount,
  trustSignals,
}: HeaderProps) {
  return (
    <header
      className="sticky top-0 z-50 w-full border-b border-border/40 bg-background/95 backdrop-blur-xl supports-[backdrop-filter]:bg-background/80 transition-all duration-300"
      aria-label="Main navigation"
    >
      {/* Trust Signals Bar */}
      {trustSignals && (
        <div
          className="border-b border-border/20 bg-muted/30"
          role="complementary"
          aria-label="Trust and security information"
        >
          <div className="container mx-auto px-4 py-2">
            <TrustSignals {...trustSignals} />
          </div>
        </div>
      )}

      <div className="container mx-auto px-4">
        <div className="flex h-16 lg:h-20 items-center justify-between">
          {/* Desktop Navigation */}
          <div className="hidden lg:flex lg:items-center lg:gap-8">
            {/* Brand */}
            <Link to={brand.url} className="flex items-center gap-3 group shrink-0">
              {brand.logo ? (
                <img
                  src={brand.logo}
                  alt={brand.title}
                  className="h-8 w-8 transition-transform group-hover:scale-110"
                />
              ) : (
                <div className="h-8 w-8 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center transition-all group-hover:scale-110 group-hover:shadow-lg group-hover:shadow-blue-500/25">
                  <span className="text-white font-bold text-sm">AI</span>
                </div>
              )}
              <div className="flex flex-col">
                <span className="text-lg font-bold text-foreground">{brand.title}</span>
              </div>
            </Link>

            {/* Navigation Menu */}
            <NavigationMenu className="mx-6" aria-label="Primary navigation">
              <NavigationMenuList>
                {navigation.map((item, i) => {
                  if (item.children && item.children.length > 0) {
                    return (
                      <NavigationMenuItem key={`${item.title}-${i}`}>
                        <NavigationMenuTrigger className="text-foreground font-medium">
                          {item.icon && <span className="mr-2">{item.icon}</span>}
                          {item.title}
                        </NavigationMenuTrigger>
                        <NavigationMenuContent>
                          <ul className="w-80 p-3">
                            {item.children.map((child, ii) => (
                              <li key={`${child.title}-${ii}`}>
                                <NavigationMenuLink asChild>
                                  <Link
                                    to={child.url}
                                    target={child.target}
                                    className="flex select-none gap-4 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
                                  >
                                    {child.icon && (
                                      <span className="mt-1 shrink-0">{child.icon}</span>
                                    )}
                                    <div>
                                      <div className="text-sm font-semibold">{child.title}</div>
                                      {child.description && (
                                        <p className="text-sm leading-snug text-muted-foreground">
                                          {child.description}
                                        </p>
                                      )}
                                    </div>
                                  </Link>
                                </NavigationMenuLink>
                              </li>
                            ))}
                          </ul>
                        </NavigationMenuContent>
                      </NavigationMenuItem>
                    );
                  }

                  return (
                    <NavigationMenuItem key={`${item.title}-${i}`}>
                      <NavigationMenuLink asChild>
                        <Link
                          to={item.url}
                          target={item.target}
                          className={cn(
                            "text-foreground font-medium flex items-center",
                            navigationMenuTriggerStyle(),
                            buttonVariants({ variant: "ghost" })
                          )}
                        >
                          {item.icon && <span className="mr-2">{item.icon}</span>}
                          {item.title}
                        </Link>
                      </NavigationMenuLink>
                    </NavigationMenuItem>
                  );
                })}
              </NavigationMenuList>
            </NavigationMenu>
          </div>

          {/* Center Search (Desktop) */}
          <div
            className="hidden lg:flex flex-1 justify-center max-w-md mx-8"
            aria-label="Site search"
          >
            {search && <SearchBar {...search} />}
          </div>

          {/* Desktop Actions */}
          <div className="hidden lg:flex lg:items-center lg:gap-3 shrink-0">
            {/* User Account or Auth Buttons */}
            {userAccount ? (
              <UserAccountMenu {...userAccount} />
            ) : (
              buttons.map((button, i) => (
                <Button
                  key={`${button.text || button.label}-${i}`}
                  variant={button.variant}
                  asChild
                  className={cn(
                    "transition-all duration-200",
                    i === 0 && "bg-primary hover:bg-primary/90 text-primary-foreground font-medium"
                  )}
                >
                  <Link to={button.url} target={button.target} className="flex items-center gap-2">
                    {button.title}
                  </Link>
                </Button>
              ))
            )}
          </div>

          {/* Mobile Navigation */}
          <div className="flex lg:hidden items-center justify-between w-full">
            {/* Mobile Brand */}
            <Link to={brand.url} className="flex items-center gap-2 group">
              {brand.logo ? (
                <img
                  src={brand.logo}
                  alt={brand.title}
                  className="h-8 w-8 transition-transform group-hover:scale-110"
                />
              ) : (
                <div className="h-8 w-8 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center transition-all group-hover:scale-110">
                  <span className="text-white font-bold text-sm">AI</span>
                </div>
              )}
              <span className="text-lg font-bold text-foreground">{brand.title}</span>
            </Link>

            {/* Mobile Search and Menu */}
            <div className="flex items-center gap-2">
              {search && (
                <Button variant="ghost" size="icon" className="md:hidden">
                  <Search className="h-5 w-5" />
                  <span className="sr-only">Search</span>
                </Button>
              )}

              {/* Mobile Menu */}
              <Sheet>
                <SheetTrigger asChild>
                  <Button variant="ghost" size="icon">
                    <Menu className="h-5 w-5" />
                    <span className="sr-only">Toggle menu</span>
                  </Button>
                </SheetTrigger>
                <SheetContent side="right" className="w-80 overflow-y-auto">
                  <SheetHeader className="border-b pb-4">
                    <SheetTitle>
                      <Link to={brand.url} className="flex items-center gap-2">
                        {brand.logo ? (
                          <img src={brand.logo} alt={brand.title} className="h-8 w-8" />
                        ) : (
                          <div className="h-8 w-8 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                            <span className="text-white font-bold text-sm">AI</span>
                          </div>
                        )}
                        <span className="text-lg font-bold">{brand.title}</span>
                      </Link>
                    </SheetTitle>
                  </SheetHeader>

                  {/* Mobile Search */}
                  {search && (
                    <div className="py-4 border-b">
                      <SearchBar {...search} className="w-full" />
                    </div>
                  )}

                  <div className="mt-6 flex flex-col gap-4">
                    <Accordion type="single" collapsible className="w-full">
                      {navigation.map((item, i) => {
                        if (item.children && item.children.length > 0) {
                          return (
                            <AccordionItem
                              key={`${item.title}-${i}`}
                              value={item.title}
                              className="border-b-0"
                            >
                              <AccordionTrigger className="py-3 font-semibold hover:no-underline text-left">
                                <span className="flex items-center">
                                  {item.icon && <span className="mr-2">{item.icon}</span>}
                                  {item.title}
                                </span>
                              </AccordionTrigger>
                              <AccordionContent className="pb-2">
                                {item.children.map((child, ii) => (
                                  <Link
                                    key={ii}
                                    to={child.url}
                                    target={child.target}
                                    className="flex select-none gap-3 rounded-md p-3 leading-none outline-none transition-colors hover:bg-accent hover:text-accent-foreground"
                                  >
                                    {child.icon && (
                                      <span className="mt-1 shrink-0">{child.icon}</span>
                                    )}
                                    <div>
                                      <div className="text-sm font-semibold">{child.title}</div>
                                      {child.description && (
                                        <p className="text-sm leading-snug text-muted-foreground">
                                          {child.description}
                                        </p>
                                      )}
                                    </div>
                                  </Link>
                                ))}
                              </AccordionContent>
                            </AccordionItem>
                          );
                        }

                        return (
                          <Link
                            key={i}
                            to={item.url}
                            target={item.target}
                            className="flex items-center gap-2 py-3 font-semibold hover:text-primary transition-colors"
                          >
                            {item.icon && <span>{item.icon}</span>}
                            {item.title}
                          </Link>
                        );
                      })}
                    </Accordion>

                    <div className="border-t pt-6 mt-4">
                      {/* Mobile User Account or Auth Buttons */}
                      {userAccount ? (
                        <div className="mb-6">
                          <UserAccountMenu {...userAccount} />
                        </div>
                      ) : (
                        <div className="flex flex-col gap-3 mb-6">
                          {buttons.map((button, i) => (
                            <Button key={i} variant={button.variant} asChild className="w-full">
                              <Link to={button.url} target={button.target}>
                                {button.title}
                              </Link>
                            </Button>
                          ))}
                        </div>
                      )}

                      <div className="flex items-center justify-between pt-4 border-t">
                        {/* Theme and language controls removed */}
                      </div>
                    </div>
                  </div>
                </SheetContent>
              </Sheet>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
}

# 数据库设计优化总结

## 🎯 优化目标

结合 ShipAny 和 Remix-Supabase 项目的优点，对当前数据库设计进行全面优化，提升系统的可扩展性、安全性和功能完整性。

## 📊 优化前后对比

### 优化前 (原始设计)
- ✅ 基础用户管理
- ✅ 简单订单系统
- ✅ 基础积分系统
- ❌ 缺乏多租户支持
- ❌ 权限系统简单
- ❌ 联盟营销功能不完善

### 优化后 (新设计)
- ✅ **企业级多租户架构**
- ✅ **完整的RBAC权限系统**
- ✅ **增强的联盟营销系统**
- ✅ **API密钥管理**
- ✅ **多计费提供商支持**
- ✅ **订阅与一次性支付分离**
- ✅ **通知系统**
- ✅ **正式邀请系统**
- ✅ **积分过期机制**

## 🏗️ 新增核心表结构

### 1. 多租户支持
```sql
-- 账户表 (支持个人和团队账户)
accounts (id, name, slug, is_personal_account, primary_owner_user_id)

-- 账户成员关系表
accounts_memberships (account_id, user_id, account_role)
```

### 2. RBAC权限系统
```sql
-- 角色表
roles (name, hierarchy_level)

-- 角色权限表
role_permissions (role, permission)

-- 权限枚举
app_permissions: 'roles.manage' | 'billing.manage' | 'settings.manage' | 'members.manage' | 'invites.manage' | 'api.manage'
```

### 3. 联盟营销系统
```sql
-- 联盟表 (来自ShipAny设计)
affiliates (
  user_uuid, invited_by, paid_order_no, 
  paid_amount, reward_percent, reward_amount, status
)
```

### 4. API管理
```sql
-- API密钥表
api_keys (
  api_key, title, user_uuid, account_id, 
  status, last_used_at, expires_at
)
```

### 5. 增强的计费系统
```sql
-- 计费客户表 (多提供商支持)
billing_customers (account_id, customer_id, provider)

-- 订单表 (一次性支付)
orders (id, account_id, billing_customer_id, total_amount, status, billing_provider)

-- 订阅表 (周期性支付)
subscriptions (id, account_id, status, billing_provider, period_starts_at, period_ends_at)

-- 支持的计费提供商
billing_provider: 'stripe' | 'lemon-squeezy' | 'paddle'
```

### 6. 通知系统
```sql
-- 通知表
notifications (
  account_id, user_id, title, body, type, 
  channel, dismissed, read_at, expires_at
)
```

### 7. 正式邀请系统
```sql
-- 邀请表
invitations (
  account_id, email, role, invite_token, 
  invited_by, expires_at, accepted_at
)
```

### 8. 增强的积分系统
```sql
-- 积分交易表 (添加过期机制)
credit_transactions (
  trans_no, user_uuid, account_id, trans_type, 
  credits, order_no, expires_at
)
```

## 🚀 主要改进点

### 1. **多租户架构**
- 支持个人账户和团队账户
- 灵活的成员管理
- 账户级别的数据隔离

### 2. **企业级权限管理**
- 基于角色的访问控制 (RBAC)
- 层级化角色系统
- 细粒度权限控制

### 3. **完善的计费系统**
- 支持多个计费提供商
- 订阅和一次性支付分离
- 详细的订单项管理

### 4. **增强的用户体验**
- 应用内通知系统
- 正式的邀请流程
- API访问管理

### 5. **强化的营销功能**
- 完整的联盟营销系统
- 积分过期机制
- 详细的交易记录

## 📈 业务价值

1. **可扩展性**: 多租户架构支持B2B业务模式
2. **安全性**: RBAC权限系统提供企业级安全保障
3. **灵活性**: 多计费提供商支持降低vendor lock-in风险
4. **用户体验**: 通知系统和邀请系统提升用户参与度
5. **商业化**: 联盟营销系统促进用户增长

## 🔄 迁移建议

1. **渐进式迁移**: 先添加新表，保持向后兼容
2. **数据迁移**: 将现有用户数据迁移到新的多租户结构
3. **权限初始化**: 为现有用户分配默认角色和权限
4. **测试验证**: 确保所有功能在新架构下正常工作

## 📝 下一步行动

1. 运行数据库迁移脚本
2. 更新相关的服务层代码
3. 实现新的权限检查逻辑
4. 添加多租户相关的UI组件
5. 编写相应的单元测试和集成测试

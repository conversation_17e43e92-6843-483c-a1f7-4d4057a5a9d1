import { describe, expect, it } from "vitest";
import {
  AI_PROVIDERS,
  getAvailableProviders,
  getProviderModels,
  supportsReasoning,
  validateProviderModel,
} from "../ai-providers";

describe("AI Providers", () => {
  describe("validateProviderModel", () => {
    it("should validate correct provider-model combinations", () => {
      expect(validateProviderModel("openai", "gpt-4o")).toBe(true);
      expect(validateProviderModel("deepseek", "deepseek-chat")).toBe(true);
      expect(validateProviderModel("openrouter", "deepseek/deepseek-r1")).toBe(true);
    });

    it("should reject invalid provider-model combinations", () => {
      expect(validateProviderModel("openai", "invalid-model")).toBe(false);
      expect(validateProviderModel("deepseek", "gpt-4o")).toBe(false);
      // @ts-expect-error - testing invalid provider
      expect(validateProviderModel("invalid-provider", "any-model")).toBe(false);
    });
  });

  describe("getProviderModels", () => {
    it("should return correct models for each provider", () => {
      const openaiModels = getProviderModels("openai");
      expect(openaiModels).toContain("gpt-4o");
      expect(openaiModels).toContain("gpt-4o-mini");

      const deepseekModels = getProviderModels("deepseek");
      expect(deepseekModels).toContain("deepseek-chat");
      expect(deepseekModels).toContain("deepseek-r1");
    });

    it("should return empty array for invalid provider", () => {
      // @ts-expect-error - testing invalid provider
      const models = getProviderModels("invalid-provider");
      expect(models).toEqual([]);
    });
  });

  describe("supportsReasoning", () => {
    it("should correctly identify reasoning models", () => {
      expect(supportsReasoning("openai", "o1-preview")).toBe(true);
      expect(supportsReasoning("deepseek", "deepseek-r1")).toBe(true);
      expect(supportsReasoning("openrouter", "deepseek/deepseek-r1")).toBe(true);
    });

    it("should return false for non-reasoning models", () => {
      expect(supportsReasoning("openai", "gpt-4o")).toBe(false);
      expect(supportsReasoning("deepseek", "deepseek-chat")).toBe(false);
      expect(supportsReasoning("replicate", "meta/llama-2-70b-chat")).toBe(false);
    });
  });

  describe("getAvailableProviders", () => {
    it("should return all available providers", () => {
      const providers = getAvailableProviders();
      expect(providers).toHaveLength(5);

      const providerNames = providers.map((p) => p.name);
      expect(providerNames).toContain("openai");
      expect(providerNames).toContain("deepseek");
      expect(providerNames).toContain("openrouter");
      expect(providerNames).toContain("siliconflow");
      expect(providerNames).toContain("replicate");
    });

    it("should have correct provider configurations", () => {
      const providers = getAvailableProviders();

      const openaiProvider = providers.find((p) => p.name === "openai");
      expect(openaiProvider).toBeDefined();
      expect(openaiProvider?.displayName).toBe("OpenAI");
      expect(openaiProvider?.requiresApiKey).toBe(true);
      expect(openaiProvider?.supportsReasoning).toBe(true);
    });
  });

  describe("AI_PROVIDERS configuration", () => {
    it("should have all required properties for each provider", () => {
      Object.values(AI_PROVIDERS).forEach((provider) => {
        expect(provider).toHaveProperty("name");
        expect(provider).toHaveProperty("displayName");
        expect(provider).toHaveProperty("models");
        expect(provider).toHaveProperty("requiresApiKey");

        expect(typeof provider.name).toBe("string");
        expect(typeof provider.displayName).toBe("string");
        expect(Array.isArray(provider.models)).toBe(true);
        expect(typeof provider.requiresApiKey).toBe("boolean");
        expect(provider.models.length).toBeGreaterThan(0);
      });
    });

    it("should have reasoning configuration for providers that support it", () => {
      const reasoningProviders = Object.values(AI_PROVIDERS).filter((p) => p.supportsReasoning);

      reasoningProviders.forEach((provider) => {
        expect(provider.reasoningModels).toBeDefined();
        expect(Array.isArray(provider.reasoningModels)).toBe(true);
        expect(provider.reasoningModels!.length).toBeGreaterThan(0);
      });
    });
  });
});

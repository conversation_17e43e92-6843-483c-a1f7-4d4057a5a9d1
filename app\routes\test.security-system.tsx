/**
 * Security System Test Page
 * Demonstrates security functionality and provides testing interface
 */

import { <PERSON> } from "@remix-run/react";
import {
  Activity,
  AlertTriangle,
  Ban,
  CheckCircle,
  ExternalLink,
  Eye,
  FileText,
  Filter,
  Globe,
  Info,
  Lock,
  Search,
  Server,
  Settings,
  Shield,
  Users,
  XCircle,
  Zap,
} from "lucide-react";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";

export default function SecuritySystemTest() {
  const features = [
    {
      name: "Security Middleware",
      description: "Comprehensive security controls and threat detection",
      status: "completed",
      icon: Shield,
      features: [
        "✅ Request security validation and filtering",
        "✅ SQL injection detection and prevention",
        "✅ XSS attack detection and blocking",
        "✅ Path traversal protection",
        "✅ Command injection detection",
        "✅ CORS policy enforcement",
        "✅ Security headers implementation",
      ],
    },
    {
      name: "Rate Limiting",
      description: "Advanced API rate limiting and abuse prevention",
      status: "completed",
      icon: Zap,
      features: [
        "✅ Sliding window rate limiting algorithm",
        "✅ Per-endpoint rate limit configuration",
        "✅ User-based and IP-based limiting",
        "✅ Automatic cleanup of expired entries",
        "✅ Rate limit headers and responses",
        "✅ Configurable rate limit policies",
        "✅ Real-time rate limit monitoring",
      ],
    },
    {
      name: "Input Validation",
      description: "Comprehensive input validation and sanitization",
      status: "completed",
      icon: FileText,
      features: [
        "✅ Zod schema-based validation",
        "✅ HTML sanitization and XSS prevention",
        "✅ Content type validation",
        "✅ Request size limits",
        "✅ Unicode normalization",
        "✅ Threat pattern detection",
        "✅ Custom validation rules",
      ],
    },
    {
      name: "Audit Logging",
      description: "Security event logging and monitoring system",
      status: "completed",
      icon: Activity,
      features: [
        "✅ Comprehensive security event logging",
        "✅ Authentication and authorization tracking",
        "✅ Security violation detection and logging",
        "✅ Risk scoring and threat assessment",
        "✅ Real-time security metrics",
        "✅ Critical event alerting",
        "✅ Audit log management and cleanup",
      ],
    },
    {
      name: "Permission System",
      description: "Role-based access control and authorization",
      status: "completed",
      icon: Lock,
      features: [
        "✅ Role-based permission system",
        "✅ Resource-level access control",
        "✅ Custom permission assignments",
        "✅ Permission inheritance and validation",
        "✅ Admin and user role separation",
        "✅ Self-access permission checks",
        "✅ Permission error handling",
      ],
    },
    {
      name: "Security Dashboard",
      description: "Admin security monitoring and management interface",
      status: "completed",
      icon: Settings,
      features: [
        "✅ Real-time security metrics dashboard",
        "✅ Audit event monitoring and filtering",
        "✅ Threat detection and analysis",
        "✅ Rate limit monitoring and management",
        "✅ IP risk assessment and blocking",
        "✅ System status monitoring",
        "✅ Security configuration management",
      ],
    },
  ];

  const securityTypes = [
    {
      type: "middleware",
      name: "Security Middleware",
      icon: Shield,
      color: "text-blue-500",
      description: "Request-level security validation and protection",
      features: [
        "Threat detection and blocking",
        "CORS policy enforcement",
        "Security headers implementation",
        "Content type validation",
        "Request size limits",
        "IP-based access control",
      ],
    },
    {
      type: "rate-limiting",
      name: "Rate Limiting",
      icon: Zap,
      color: "text-yellow-500",
      description: "API abuse prevention and traffic control",
      features: [
        "Sliding window algorithm",
        "Per-endpoint configuration",
        "User and IP-based limits",
        "Automatic cleanup",
        "Rate limit headers",
        "Real-time monitoring",
      ],
    },
    {
      type: "validation",
      name: "Input Validation",
      icon: FileText,
      color: "text-green-500",
      description: "Input sanitization and validation",
      features: [
        "Schema-based validation",
        "HTML sanitization",
        "XSS prevention",
        "SQL injection detection",
        "Content filtering",
        "Threat pattern matching",
      ],
    },
    {
      type: "audit",
      name: "Audit Logging",
      icon: Activity,
      color: "text-purple-500",
      description: "Security event tracking and monitoring",
      features: [
        "Comprehensive event logging",
        "Risk scoring system",
        "Real-time metrics",
        "Critical event alerts",
        "Log management",
        "Threat analysis",
      ],
    },
  ];

  const apiEndpoints = [
    {
      method: "GET",
      endpoint: "/api/security?action=metrics",
      description: "Get security metrics and statistics",
      example: "curl '/api/security?action=metrics'",
    },
    {
      method: "GET",
      endpoint: "/api/security?action=audit-events",
      description: "Get audit events with filtering",
      example: "curl '/api/security?action=audit-events&limit=50&severity=HIGH'",
    },
    {
      method: "GET",
      endpoint: "/api/security?action=rate-limits",
      description: "Get rate limit status and statistics",
      example: "curl '/api/security?action=rate-limits'",
    },
    {
      method: "GET",
      endpoint: "/api/security?action=threat-check",
      description: "Check input for security threats",
      example: "curl '/api/security?action=threat-check&input=<script>alert(1)</script>'",
    },
    {
      method: "POST",
      endpoint: "/api/security",
      description: "Perform security management actions",
      example: "curl -X POST '/api/security' -d '{\"action\":\"clear-rate-limits\"}'",
    },
    {
      method: "GET",
      endpoint: "/api/security?action=system-status",
      description: "Get security system status",
      example: "curl '/api/security?action=system-status'",
    },
  ];

  const testScenarios = [
    {
      name: "Security Middleware Testing",
      description: "Test security middleware and threat detection",
      steps: [
        "1. Test SQL injection detection with malicious inputs",
        "2. Test XSS protection with script injection attempts",
        "3. Test path traversal protection with directory traversal",
        "4. Test CORS policy enforcement with cross-origin requests",
        "5. Verify security headers in responses",
        "6. Test content type validation and filtering",
      ],
      icon: Shield,
    },
    {
      name: "Rate Limiting Testing",
      description: "Test API rate limiting and abuse prevention",
      steps: [
        "1. Test rate limits with rapid API requests",
        "2. Verify rate limit headers in responses",
        "3. Test different endpoint rate limit configurations",
        "4. Test user-based vs IP-based rate limiting",
        "5. Verify automatic cleanup of expired entries",
        "6. Test rate limit reset functionality",
      ],
      icon: Zap,
    },
    {
      name: "Input Validation Testing",
      description: "Test input validation and sanitization",
      steps: [
        "1. Test schema validation with invalid inputs",
        "2. Test HTML sanitization and XSS prevention",
        "3. Test threat pattern detection",
        "4. Verify content filtering and cleanup",
        "5. Test custom validation rules",
        "6. Check error handling and responses",
      ],
      icon: FileText,
    },
    {
      name: "Audit Logging Testing",
      description: "Test security event logging and monitoring",
      steps: [
        "1. Generate various security events",
        "2. Verify event logging and categorization",
        "3. Test risk scoring and threat assessment",
        "4. Check real-time metrics updates",
        "5. Test critical event alerting",
        "6. Verify audit log management",
      ],
      icon: Activity,
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";
      case "in-progress":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-4">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            Security System Test Suite
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            Test and explore the comprehensive security enhancement system
          </p>
        </div>

        {/* Quick Navigation */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Shield className="w-5 h-5" />
              <span>Quick Navigation</span>
            </CardTitle>
            <CardDescription>Direct links to security dashboards and features</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              <Button
                asChild
                variant="outline"
                className="h-auto p-4 flex flex-col items-center space-y-2"
              >
                <Link to="/admin/security">
                  <Shield className="w-6 h-6" />
                  <span className="text-sm font-medium text-center">Security Dashboard</span>
                </Link>
              </Button>
              <Button
                asChild
                variant="outline"
                className="h-auto p-4 flex flex-col items-center space-y-2"
              >
                <Link to="/api/security?action=metrics">
                  <Activity className="w-6 h-6" />
                  <span className="text-sm font-medium text-center">Security Metrics</span>
                </Link>
              </Button>
              <Button
                asChild
                variant="outline"
                className="h-auto p-4 flex flex-col items-center space-y-2"
              >
                <Link to="/api/security?action=audit-events">
                  <FileText className="w-6 h-6" />
                  <span className="text-sm font-medium text-center">Audit Events</span>
                </Link>
              </Button>
              <Button
                asChild
                variant="outline"
                className="h-auto p-4 flex flex-col items-center space-y-2"
              >
                <Link to="/api/security?action=system-status">
                  <Server className="w-6 h-6" />
                  <span className="text-sm font-medium text-center">System Status</span>
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Implementation Status */}
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            Implementation Status
          </h2>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {features.map((feature) => (
              <Card key={feature.name}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center space-x-2">
                      <feature.icon className="w-5 h-5" />
                      <span>{feature.name}</span>
                    </CardTitle>
                    <Badge className={getStatusColor(feature.status)}>
                      <div className="flex items-center space-x-1">
                        <CheckCircle className="w-4 h-4" />
                        <span className="capitalize">{feature.status}</span>
                      </div>
                    </Badge>
                  </div>
                  <CardDescription>{feature.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {feature.features.map((item, index) => (
                      <div key={index} className="text-sm text-gray-600 dark:text-gray-400">
                        {item}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Security Types */}
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            Security Components
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {securityTypes.map((type) => (
              <Card key={type.type}>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <type.icon className={`w-5 h-5 ${type.color}`} />
                    <span>{type.name}</span>
                  </CardTitle>
                  <CardDescription>{type.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {type.features.map((feature, index) => (
                      <div
                        key={index}
                        className="text-sm text-gray-600 dark:text-gray-400 flex items-center"
                      >
                        <div className="w-2 h-2 bg-blue-500 rounded-full mr-2" />
                        {feature}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* API Endpoints */}
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">API Endpoints</h2>
          <div className="space-y-4">
            {apiEndpoints.map((endpoint, index) => (
              <Card key={index}>
                <CardHeader>
                  <div className="flex items-center space-x-3">
                    <Badge variant="outline">{endpoint.method}</Badge>
                    <code className="text-sm font-mono">{endpoint.endpoint}</code>
                  </div>
                  <CardDescription>{endpoint.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="bg-gray-100 dark:bg-gray-800 p-3 rounded">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-semibold">Example:</span>
                      <Button variant="outline" size="sm">
                        <ExternalLink className="w-4 h-4" />
                      </Button>
                    </div>
                    <code className="text-sm">{endpoint.example}</code>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Test Scenarios */}
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Test Scenarios</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {testScenarios.map((scenario) => (
              <Card key={scenario.name}>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <scenario.icon className="w-5 h-5" />
                    <span>{scenario.name}</span>
                  </CardTitle>
                  <CardDescription>{scenario.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {scenario.steps.map((step, index) => (
                      <div key={index} className="text-sm text-gray-600 dark:text-gray-400">
                        {step}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Testing Instructions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Info className="w-5 h-5" />
              <span>Testing Instructions</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                <h4 className="font-semibold text-blue-900 dark:text-blue-400 mb-2">
                  How to Test Security System:
                </h4>
                <ol className="text-sm text-blue-800 dark:text-blue-300 space-y-1 list-decimal list-inside">
                  <li>Access the security dashboard to view real-time metrics</li>
                  <li>Test API endpoints with various security scenarios</li>
                  <li>Attempt malicious inputs to verify threat detection</li>
                  <li>Test rate limiting with rapid API requests</li>
                  <li>Verify audit logging and event tracking</li>
                  <li>Check permission system and access controls</li>
                </ol>
              </div>

              <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                <h4 className="font-semibold text-green-900 dark:text-green-400 mb-2">
                  Key Security Features to Test:
                </h4>
                <ul className="text-sm text-green-800 dark:text-green-300 space-y-1 list-disc list-inside">
                  <li>SQL injection and XSS attack detection</li>
                  <li>Rate limiting and abuse prevention</li>
                  <li>Input validation and sanitization</li>
                  <li>Audit logging and security monitoring</li>
                  <li>Role-based access control and permissions</li>
                  <li>Security headers and CORS protection</li>
                </ul>
              </div>

              <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg">
                <h4 className="font-semibold text-yellow-900 dark:text-yellow-400 mb-2">
                  Security Testing Best Practices:
                </h4>
                <ul className="text-sm text-yellow-800 dark:text-yellow-300 space-y-1 list-disc list-inside">
                  <li>Test with various malicious input patterns</li>
                  <li>Verify proper error handling and responses</li>
                  <li>Check security headers in all responses</li>
                  <li>Test rate limiting with different user scenarios</li>
                  <li>Verify audit logging captures all security events</li>
                  <li>Test permission system with different user roles</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Start Testing */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Shield className="w-5 h-5" />
              <span>Start Testing</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-3">
              <Button asChild>
                <Link to="/admin/security" className="flex items-center space-x-2">
                  <Shield className="w-4 h-4" />
                  <span>Security Dashboard</span>
                  <ExternalLink className="w-4 h-4" />
                </Link>
              </Button>
              <Button asChild variant="outline">
                <Link to="/api/security?action=metrics" className="flex items-center space-x-2">
                  <Activity className="w-4 h-4" />
                  <span>Security Metrics</span>
                  <ExternalLink className="w-4 h-4" />
                </Link>
              </Button>
              <Button asChild variant="outline">
                <Link
                  to="/api/security?action=system-status"
                  className="flex items-center space-x-2"
                >
                  <Server className="w-4 h-4" />
                  <span>System Status</span>
                  <ExternalLink className="w-4 h-4" />
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

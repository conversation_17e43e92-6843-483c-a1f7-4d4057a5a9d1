// Homepage configuration file
export interface ExampleConversation {
  title: string;
  description: string;
  prompt: string;
  icon: string;
}

export interface ShowcaseItem {
  title: string;
  description: string;
  icon: string;
  url: string;
}

export interface HomepageConfig {
  chat: {
    title: string;
    subtitle: string;
    placeholder: string;
    helpText: string;
  };
  examples: ExampleConversation[];
  showcase: {
    title: string;
    items: ShowcaseItem[];
  };
}

export const homepageConfig: HomepageConfig = {
  chat: {
    title: "AI Assistant",
    subtitle:
      "I can help you answer questions, generate code, create content, and more. Choose an example below or enter your question directly.",
    placeholder: "Enter your question...",
    helpText: "Press Enter to send, Shift + Enter for new line",
  },
  examples: [
    {
      title: "Code Assistant",
      description: "Help you write, debug, and optimize code",
      prompt: "Please help me write a React component for displaying a user list",
      icon: "💻",
    },
    {
      title: "Content Creation",
      description: "Assist you in creating various types of text content",
      prompt: "Please help me write a product introduction copy for an AI chatbot",
      icon: "✍️",
    },
    {
      title: "Learning Assistant",
      description: "Answer various questions encountered in learning",
      prompt: "Please explain what machine learning is and its main application scenarios",
      icon: "📚",
    },
    {
      title: "Creative Inspiration",
      description: "Stimulate your creative thinking and imagination",
      prompt: "Please give me some innovative project ideas about sustainable development",
      icon: "💡",
    },
  ],
  showcase: {
    title: "Explore AI Features",
    items: [
      {
        title: "Smart Conversation",
        description:
          "Intelligent dialogue system based on the latest AI models, supporting multi-turn conversations and context understanding",
        icon: "🤖",
        url: "/dev/ai-tools",
      },
      {
        title: "Code Generation",
        description:
          "Automatically generate high-quality code, supporting multiple programming languages and frameworks",
        icon: "💻",
        url: "/dev/ai-tools",
      },
      {
        title: "Text Creation",
        description:
          "AI-driven content creation, including articles, marketing copy, creative writing, and more",
        icon: "✍️",
        url: "/dev/ai-tools",
      },
      {
        title: "Image Generation",
        description:
          "Generate high-quality images based on text descriptions, supporting multiple artistic styles",
        icon: "🎨",
        url: "/dev/ai-tools",
      },
      {
        title: "Data Analysis",
        description: "Intelligent data analysis and visualization, quickly gain business insights",
        icon: "📊",
        url: "/dev/ai-tools",
      },
      {
        title: "API Integration",
        description:
          "Simple and easy-to-use API interface, easily integrate into your applications",
        icon: "🔌",
        url: "/docs",
      },
    ],
  },
};

# User Onboarding System

## Overview

The user onboarding system provides a comprehensive welcome experience for new users, including welcome emails, guided tours, and initial credit allocation.

## Features

### ✅ Implemented Features

1. **Welcome Email System**
   - Professional HTML email template
   - Dynamic content with user data
   - Credit information and quick start tips
   - Responsive design for all devices

2. **Interactive Welcome Flow**
   - Multi-step guided tour
   - Feature highlights and explanations
   - Quick start guide with actionable steps
   - Invite code sharing and bonus information

3. **Credit Management**
   - Initial 100 credits for new users
   - +50 bonus credits for invite code usage
   - +25 referral credits for inviters
   - Automatic credit transaction logging

4. **Onboarding Tracking**
   - Database fields to track completion
   - API endpoints for status management
   - Skip functionality for power users

## File Structure

```
app/
├── components/onboarding/
│   └── welcome-flow.tsx          # Main onboarding component
├── services/
│   └── onboarding.server.ts      # Server-side onboarding logic
├── routes/
│   ├── api.onboarding.complete.tsx  # API for completion tracking
│   └── test.onboarding.tsx       # Development test page
├── lib/
│   ├── email/templates/
│   │   └── welcome.ts            # Enhanced welcome email template
│   └── db/schema.ts              # Updated with onboarding fields
└── migrations/
    └── add_onboarding_fields.sql # Database migration
```

## Usage

### For New Users

1. **Registration**: User signs up via Google OAuth or Neon Auth
2. **Welcome Email**: Automatically sent with account details
3. **Welcome Flow**: Interactive tour shown on first login
4. **Credit Allocation**: Initial credits + invite bonuses applied

### For Developers

#### Testing the Onboarding Flow

Visit `/test/onboarding` to test the welcome flow with mock data.

#### Triggering Onboarding

Add `?new_user=true` to any URL to trigger the onboarding flow for testing.

#### API Endpoints

- `POST /api/onboarding/complete` - Mark onboarding as complete
- `GET /api/onboarding/complete` - Check onboarding status

## Configuration

### Environment Variables

```bash
# Email configuration
FROM_EMAIL=<EMAIL>
RESEND_API_KEY=your_resend_api_key

# Application URLs
WEB_URL=https://yourdomain.com
```

### Database Migration

Run the migration to add onboarding fields:

```sql
-- See migrations/add_onboarding_fields.sql
ALTER TABLE users 
ADD COLUMN has_seen_onboarding BOOLEAN DEFAULT FALSE NOT NULL,
ADD COLUMN onboarding_completed_at TIMESTAMPTZ;
```

## Customization

### Welcome Email Template

Edit `app/lib/email/templates/welcome.ts` to customize:
- Email subject and content
- Branding and styling
- Call-to-action buttons
- Feature highlights

### Welcome Flow Steps

Modify `app/components/onboarding/welcome-flow.tsx` to:
- Add/remove onboarding steps
- Change feature highlights
- Customize animations and styling
- Update quick start instructions

### Credit Allocation

Adjust credit amounts in `app/services/onboarding.server.ts`:
- Base credits for new users (default: 100)
- Invite bonus credits (default: +50)
- Referral bonus credits (default: +25)

## Integration Points

### Authentication Flow

The onboarding system integrates with:
- Google OAuth authentication
- Neon Auth system
- User creation process
- Session management

### Credit System

Automatic integration with:
- Credit transaction logging
- User balance updates
- Invite code processing
- Referral tracking

### Email System

Uses the existing email infrastructure:
- Resend email service
- Template system
- Error handling
- Delivery tracking

## Best Practices

1. **Progressive Disclosure**: Show features gradually
2. **Skip Option**: Always allow users to skip onboarding
3. **Mobile Friendly**: Ensure responsive design
4. **Performance**: Lazy load onboarding components
5. **Analytics**: Track completion rates and drop-off points

## Troubleshooting

### Common Issues

1. **Email Not Sending**
   - Check RESEND_API_KEY configuration
   - Verify FROM_EMAIL domain is verified
   - Check email service logs

2. **Onboarding Not Showing**
   - Verify `new_user=true` parameter
   - Check user's `has_seen_onboarding` status
   - Ensure component is properly imported

3. **Credits Not Applied**
   - Check database transaction logs
   - Verify invite code validity
   - Review credit service error logs

### Debug Mode

Enable debug logging by setting:
```bash
NODE_ENV=development
```

## Future Enhancements

- [ ] A/B testing for different onboarding flows
- [ ] Analytics integration for completion tracking
- [ ] Personalized onboarding based on user type
- [ ] Video tutorials integration
- [ ] Progress saving for interrupted flows
- [ ] Multi-language support
- [ ] Advanced invite code features
- [ ] Gamification elements

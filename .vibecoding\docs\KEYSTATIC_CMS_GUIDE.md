# Keystatic CMS Integration Guide

This guide explains how to use the integrated Keystatic CMS for content management in your AI SaaS Starter project.

## 🎯 Overview

Keystatic is a modern, Git-based CMS that stores content as structured files in your repository. It provides a user-friendly interface for managing:

- **Blog Posts** - Articles, tutorials, and news
- **Pages** - Static pages like About, Terms, etc.
- **Testimonials** - Customer reviews and feedback
- **FAQs** - Frequently asked questions
- **Site Settings** - Global site configuration
- **Homepage Content** - Hero section, features, and stats

## 🚀 Getting Started

### 1. Access the CMS

Visit `/keystatic` in your browser to access the content management interface.

### 2. Content Types

#### Blog Posts (`/keystatic/posts`)
- **Title**: SEO-friendly slug generation
- **Content**: Rich text editor with markdown support
- **Featured Image**: Upload and manage images
- **Categories**: Organize posts by topic
- **Tags**: Add searchable keywords
- **SEO Settings**: Custom meta titles and descriptions
- **Status**: Draft, Published, or Archived

#### Pages (`/keystatic/pages`)
- **Static Pages**: About, Terms, Privacy, etc.
- **Custom URLs**: Define custom slug paths
- **Rich Content**: Full markdown editor
- **SEO Optimization**: Meta tags and descriptions

#### Testimonials (`/keystatic/testimonials`)
- **Customer Info**: Name, company, position
- **Avatar Upload**: Customer photos
- **Rating System**: 1-5 star ratings
- **Featured Flag**: Highlight on homepage

#### FAQs (`/keystatic/faqs`)
- **Categories**: Group by topic
- **Order Control**: Set display order
- **Rich Answers**: Formatted responses

#### Site Settings (`/keystatic/settings`)
- **Basic Info**: Site name, description, URL
- **Social Media**: Links to social profiles
- **Analytics**: Google Analytics and GTM IDs
- **SEO Defaults**: Default meta tags and images

#### Homepage Content (`/keystatic/homepage`)
- **Hero Section**: Title, subtitle, CTA buttons
- **Features Grid**: Service highlights with icons
- **Statistics**: Key metrics and numbers

## 📁 File Structure

Content is stored in the `content/` directory:

```
content/
├── posts/           # Blog posts (.yaml files)
├── pages/           # Static pages (.yaml files)
├── testimonials/    # Customer testimonials (.yaml files)
├── faqs/           # FAQ entries (.yaml files)
├── settings.yaml   # Global site settings
└── homepage.yaml   # Homepage content
```

## 🔧 Configuration

### Storage Options

**Local Storage (Default)**
```typescript
// keystatic.config.ts
storage: {
  kind: 'local',
}
```

**GitHub Storage**
```typescript
// keystatic.config.ts
storage: {
  kind: 'github',
  repo: 'username/repository-name',
}
```

### Image Management

Images are automatically organized:
- Blog images: `public/images/blog/`
- Page images: `public/images/pages/`
- Testimonial avatars: `public/images/testimonials/`

## 💻 Development

### Reading Content in Code

```typescript
import { getAllPosts, getPost, getSiteSettings } from '~/lib/keystatic';

// Get all published blog posts
const posts = await getAllPosts();

// Get a specific post
const post = await getPost('my-post-slug');

// Get site settings
const settings = await getSiteSettings();
```

### Content Types

All content types are fully typed with TypeScript:

```typescript
import type { BlogPost, SiteSettings, HomepageContent } from '~/lib/keystatic';
```

### Rendering Content

Blog post content uses Keystatic's DocumentRenderer:

```tsx
import { DocumentRenderer } from '@keystatic/core/renderer';

<div className="prose">
  <DocumentRenderer document={post.content} />
</div>
```

## 🎨 Customization

### Adding New Fields

Edit `keystatic.config.ts` to add new fields:

```typescript
// Add a new field to blog posts
schema: {
  // ... existing fields
  readingTime: fields.integer({
    label: 'Reading Time (minutes)',
    defaultValue: 5,
  }),
}
```

### Custom Content Types

Add new collections:

```typescript
// keystatic.config.ts
collections: {
  // ... existing collections
  products: collection({
    label: 'Products',
    slugField: 'name',
    path: 'content/products/*',
    schema: {
      name: fields.slug({ name: { label: 'Product Name' } }),
      price: fields.number({ label: 'Price' }),
      description: fields.text({ label: 'Description', multiline: true }),
    },
  }),
}
```

## 🔍 SEO Features

### Automatic SEO

- **Meta Tags**: Auto-generated from content
- **Open Graph**: Social media previews
- **Structured Data**: JSON-LD for search engines
- **Sitemap**: Auto-generated from published content

### Custom SEO

Each content type supports custom SEO fields:
- Custom meta titles and descriptions
- Keywords and tags
- Social media images
- Canonical URLs

## 📱 Responsive Design

The CMS interface is fully responsive and works on:
- Desktop computers
- Tablets
- Mobile devices

## 🔒 Security

### Access Control

For production, consider adding authentication:

```typescript
// keystatic.config.ts
storage: {
  kind: 'github',
  repo: 'username/repo',
  // Add authentication for production
}
```

### Content Validation

All content is validated before saving:
- Required fields enforcement
- Format validation
- Image size and type restrictions

## 🚀 Deployment

### Vercel/Netlify

1. Set environment variables for GitHub integration
2. Configure build commands to include content generation
3. Set up webhooks for automatic rebuilds

### Cloudflare Pages

1. Configure build settings
2. Set up GitHub integration
3. Enable automatic deployments

## 📚 Best Practices

### Content Organization

1. **Use Categories**: Organize blog posts by topic
2. **Tag Consistently**: Use consistent tagging strategy
3. **SEO Optimization**: Always fill SEO fields
4. **Image Optimization**: Compress images before upload

### Performance

1. **Image Sizes**: Use appropriate image dimensions
2. **Content Length**: Keep excerpts concise
3. **Caching**: Leverage static generation

### Workflow

1. **Draft First**: Use draft status for work-in-progress
2. **Review Process**: Review content before publishing
3. **Regular Updates**: Keep content fresh and updated

## 🆘 Troubleshooting

### Common Issues

**CMS Not Loading**
- Check if `/keystatic` route is accessible
- Verify Keystatic dependencies are installed

**Content Not Appearing**
- Ensure content status is "published"
- Check file permissions in content directory

**Images Not Displaying**
- Verify image paths in public directory
- Check image file formats (jpg, png, webp)

### Getting Help

- Check the [Keystatic Documentation](https://keystatic.com/docs)
- Review the configuration in `keystatic.config.ts`
- Check browser console for errors

## 🔄 Migration

### From Other CMS

To migrate from other CMS platforms:

1. Export content from existing CMS
2. Convert to Keystatic format
3. Import using the content creation interface
4. Update internal links and references

### Backup and Restore

Content is stored in Git, providing automatic:
- Version control
- Backup and restore
- Collaboration features
- Change tracking

---

## 📞 Support

For additional help with Keystatic CMS integration:

1. Check the official [Keystatic documentation](https://keystatic.com/docs)
2. Review the configuration files in this project
3. Test content creation and editing workflows
4. Verify SEO and performance optimizations

Happy content managing! 🎉

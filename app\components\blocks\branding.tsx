import ContentSection from "./content-section";

interface BrandItem {
  name: string;
  logo: string;
  url?: string;
}

interface BrandingProps {
  title: string;
  description: string;
  items: BrandItem[];
}

export default function Branding({ title, description, items }: BrandingProps) {
  return (
    <ContentSection
      title={title}
      description={description}
      background="muted"
      decorations={true}
      padding="md"
      headerSpacing="md"
      maxWidth="6xl"
    >
      {/* Brand logos grid */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-8 items-center">
        {items.map((brand, index) => (
          <div
            key={`${brand.name}-${index}`}
            className="group flex items-center justify-center p-6 rounded-xl bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm hover:bg-white/80 dark:hover:bg-gray-800/80 transition-all duration-300 hover:scale-105 hover:shadow-lg"
            style={{ animationDelay: `${index * 0.1}s` }}
          >
            {brand.url ? (
              <a
                href={brand.url}
                target="_blank"
                rel="noopener noreferrer"
                className="block w-full h-12 flex items-center justify-center"
              >
                <img
                  src={brand.logo}
                  alt={brand.name}
                  className="max-w-full max-h-full object-contain filter grayscale group-hover:grayscale-0 transition-all duration-300 opacity-60 group-hover:opacity-100"
                />
              </a>
            ) : (
              <div className="w-full h-12 flex items-center justify-center">
                <img
                  src={brand.logo}
                  alt={brand.name}
                  className="max-w-full max-h-full object-contain filter grayscale group-hover:grayscale-0 transition-all duration-300 opacity-60 group-hover:opacity-100"
                />
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Trust indicator */}
      <div className="text-center mt-12">
        <p className="text-sm text-muted-foreground">Trusted by leading companies worldwide</p>
      </div>
    </ContentSection>
  );
}

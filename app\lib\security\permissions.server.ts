/**
 * Role-Based Access Control (RBAC) System
 * Comprehensive permissions and authorization management
 */

export type Role = "user" | "admin" | "moderator" | "developer" | "support";

export type Permission =
  // User permissions
  | "user:read"
  | "user:update"
  | "user:delete"

  // API permissions
  | "api:read"
  | "api:write"
  | "api:admin"

  // Analytics permissions
  | "analytics:read"
  | "analytics:admin"

  // Feedback permissions
  | "feedback:read"
  | "feedback:write"
  | "feedback:admin"

  // Subscription permissions
  | "subscription:read"
  | "subscription:write"
  | "subscription:admin"

  // Credit permissions
  | "credits:read"
  | "credits:write"
  | "credits:admin"

  // Admin permissions
  | "admin:users"
  | "admin:system"
  | "admin:security"
  | "admin:analytics"
  | "admin:billing"

  // System permissions
  | "system:read"
  | "system:write"
  | "system:admin";

export interface UserPermissions {
  userUuid: string;
  role: Role;
  permissions: Permission[];
  customPermissions?: Permission[];
  restrictions?: string[];
  expiresAt?: Date;
}

export interface PermissionCheck {
  allowed: boolean;
  reason?: string;
  requiredPermission?: Permission;
  userRole?: Role;
}

export interface ResourceAccess {
  resource: string;
  action: string;
  userUuid: string;
  targetUserUuid?: string;
  metadata?: Record<string, any>;
}

/**
 * Role-based permission mappings
 */
export const ROLE_PERMISSIONS: Record<Role, Permission[]> = {
  user: [
    "user:read",
    "user:update",
    "api:read",
    "api:write",
    "analytics:read",
    "feedback:read",
    "feedback:write",
    "subscription:read",
    "subscription:write",
    "credits:read",
  ],

  moderator: [
    "user:read",
    "user:update",
    "api:read",
    "api:write",
    "analytics:read",
    "feedback:read",
    "feedback:write",
    "feedback:admin",
    "subscription:read",
    "credits:read",
  ],

  support: [
    "user:read",
    "user:update",
    "api:read",
    "analytics:read",
    "feedback:read",
    "feedback:write",
    "feedback:admin",
    "subscription:read",
    "subscription:write",
    "credits:read",
    "credits:write",
  ],

  developer: [
    "user:read",
    "user:update",
    "api:read",
    "api:write",
    "api:admin",
    "analytics:read",
    "analytics:admin",
    "feedback:read",
    "feedback:write",
    "subscription:read",
    "credits:read",
    "system:read",
    "system:write",
  ],

  admin: [
    "user:read",
    "user:update",
    "user:delete",
    "api:read",
    "api:write",
    "api:admin",
    "analytics:read",
    "analytics:admin",
    "feedback:read",
    "feedback:write",
    "feedback:admin",
    "subscription:read",
    "subscription:write",
    "subscription:admin",
    "credits:read",
    "credits:write",
    "credits:admin",
    "admin:users",
    "admin:system",
    "admin:security",
    "admin:analytics",
    "admin:billing",
    "system:read",
    "system:write",
    "system:admin",
  ],
};

/**
 * Get permissions for a role
 */
export function getRolePermissions(role: Role): Permission[] {
  return ROLE_PERMISSIONS[role] || [];
}

/**
 * Get user permissions including role-based and custom permissions
 */
export function getUserPermissions(
  userRole: Role,
  customPermissions: Permission[] = []
): Permission[] {
  const rolePermissions = getRolePermissions(userRole);
  const allPermissions = [...rolePermissions, ...customPermissions];

  // Remove duplicates
  return Array.from(new Set(allPermissions));
}

/**
 * Check if user has a specific permission
 */
export function hasPermission(
  userRole: Role,
  requiredPermission: Permission,
  customPermissions: Permission[] = []
): PermissionCheck {
  const userPermissions = getUserPermissions(userRole, customPermissions);
  const allowed = userPermissions.includes(requiredPermission);

  return {
    allowed,
    reason: allowed ? undefined : `Missing required permission: ${requiredPermission}`,
    requiredPermission,
    userRole,
  };
}

/**
 * Check if user has any of the specified permissions
 */
export function hasAnyPermission(
  userRole: Role,
  requiredPermissions: Permission[],
  customPermissions: Permission[] = []
): PermissionCheck {
  const userPermissions = getUserPermissions(userRole, customPermissions);
  const hasAny = requiredPermissions.some((permission) => userPermissions.includes(permission));

  return {
    allowed: hasAny,
    reason: hasAny
      ? undefined
      : `Missing any of required permissions: ${requiredPermissions.join(", ")}`,
    userRole,
  };
}

/**
 * Check if user has all of the specified permissions
 */
export function hasAllPermissions(
  userRole: Role,
  requiredPermissions: Permission[],
  customPermissions: Permission[] = []
): PermissionCheck {
  const userPermissions = getUserPermissions(userRole, customPermissions);
  const missingPermissions = requiredPermissions.filter(
    (permission) => !userPermissions.includes(permission)
  );
  const allowed = missingPermissions.length === 0;

  return {
    allowed,
    reason: allowed ? undefined : `Missing required permissions: ${missingPermissions.join(", ")}`,
    userRole,
  };
}

/**
 * Check resource access permissions
 */
export function checkResourceAccess(
  access: ResourceAccess,
  userRole: Role,
  customPermissions: Permission[] = []
): PermissionCheck {
  const { resource, action, userUuid, targetUserUuid } = access;

  // Self-access check - users can generally access their own resources
  if (targetUserUuid && userUuid === targetUserUuid) {
    const selfPermission = getSelfAccessPermission(resource, action);
    if (selfPermission) {
      return hasPermission(userRole, selfPermission, customPermissions);
    }
  }

  // Admin access check
  const adminPermission = getAdminAccessPermission(resource, action);
  if (adminPermission) {
    const adminCheck = hasPermission(userRole, adminPermission, customPermissions);
    if (adminCheck.allowed) {
      return adminCheck;
    }
  }

  // General permission check
  const generalPermission = getGeneralAccessPermission(resource, action);
  if (generalPermission) {
    return hasPermission(userRole, generalPermission, customPermissions);
  }

  return {
    allowed: false,
    reason: `No permission found for ${action} on ${resource}`,
    userRole,
  };
}

/**
 * Get self-access permission for resource and action
 */
function getSelfAccessPermission(resource: string, action: string): Permission | null {
  const permissionMap: Record<string, Record<string, Permission>> = {
    user: {
      read: "user:read",
      update: "user:update",
    },
    analytics: {
      read: "analytics:read",
    },
    feedback: {
      read: "feedback:read",
      write: "feedback:write",
    },
    subscription: {
      read: "subscription:read",
      write: "subscription:write",
    },
    credits: {
      read: "credits:read",
    },
    api: {
      read: "api:read",
      write: "api:write",
    },
  };

  return permissionMap[resource]?.[action] || null;
}

/**
 * Get admin access permission for resource and action
 */
function getAdminAccessPermission(resource: string, action: string): Permission | null {
  const adminPermissionMap: Record<string, Record<string, Permission>> = {
    user: {
      read: "admin:users",
      update: "admin:users",
      delete: "admin:users",
    },
    analytics: {
      read: "admin:analytics",
      admin: "admin:analytics",
    },
    feedback: {
      admin: "feedback:admin",
    },
    subscription: {
      admin: "subscription:admin",
    },
    credits: {
      admin: "credits:admin",
    },
    system: {
      read: "admin:system",
      write: "admin:system",
      admin: "admin:system",
    },
    security: {
      read: "admin:security",
      write: "admin:security",
      admin: "admin:security",
    },
  };

  return adminPermissionMap[resource]?.[action] || null;
}

/**
 * Get general access permission for resource and action
 */
function getGeneralAccessPermission(resource: string, action: string): Permission | null {
  const generalPermissionMap: Record<string, Record<string, Permission>> = {
    api: {
      read: "api:read",
      write: "api:write",
      admin: "api:admin",
    },
    analytics: {
      read: "analytics:read",
      admin: "analytics:admin",
    },
    feedback: {
      read: "feedback:read",
      write: "feedback:write",
      admin: "feedback:admin",
    },
    system: {
      read: "system:read",
      write: "system:write",
      admin: "system:admin",
    },
  };

  return generalPermissionMap[resource]?.[action] || null;
}

/**
 * Check if user is admin
 */
export function isAdmin(userRole: Role): boolean {
  return userRole === "admin";
}

/**
 * Check if user is moderator or higher
 */
export function isModerator(userRole: Role): boolean {
  return ["admin", "moderator", "support", "developer"].includes(userRole);
}

/**
 * Check if user can access admin features
 */
export function canAccessAdmin(
  userRole: Role,
  customPermissions: Permission[] = []
): PermissionCheck {
  return hasAnyPermission(
    userRole,
    ["admin:users", "admin:system", "admin:security", "admin:analytics", "admin:billing"],
    customPermissions
  );
}

/**
 * Create permission middleware
 */
export function requirePermission(requiredPermission: Permission) {
  return (userRole: Role, customPermissions: Permission[] = []): PermissionCheck => {
    return hasPermission(userRole, requiredPermission, customPermissions);
  };
}

/**
 * Create resource access middleware
 */
export function requireResourceAccess(resource: string, action: string) {
  return (
    userUuid: string,
    userRole: Role,
    targetUserUuid?: string,
    customPermissions: Permission[] = []
  ): PermissionCheck => {
    return checkResourceAccess(
      {
        resource,
        action,
        userUuid,
        targetUserUuid,
      },
      userRole,
      customPermissions
    );
  };
}

/**
 * Permission error response
 */
export function createPermissionErrorResponse(check: PermissionCheck): Response {
  return new Response(
    JSON.stringify({
      error: "Permission denied",
      message: check.reason || "You do not have permission to access this resource",
      code: 403,
      requiredPermission: check.requiredPermission,
      userRole: check.userRole,
    }),
    {
      status: 403,
      headers: {
        "Content-Type": "application/json",
      },
    }
  );
}

/**
 * Get user role from user data (mock implementation)
 */
export function getUserRole(userUuid: string): Role {
  // TODO: Implement actual role lookup from database
  // For now, return 'user' as default, 'admin' for specific test users
  if (userUuid === "admin-user-uuid" || userUuid.includes("admin")) {
    return "admin";
  }
  return "user";
}

/**
 * Common permission checks
 */
export const permissions = {
  // User permissions
  readUser: requirePermission("user:read"),
  updateUser: requirePermission("user:update"),
  deleteUser: requirePermission("user:delete"),

  // API permissions
  readApi: requirePermission("api:read"),
  writeApi: requirePermission("api:write"),
  adminApi: requirePermission("api:admin"),

  // Analytics permissions
  readAnalytics: requirePermission("analytics:read"),
  adminAnalytics: requirePermission("analytics:admin"),

  // Feedback permissions
  readFeedback: requirePermission("feedback:read"),
  writeFeedback: requirePermission("feedback:write"),
  adminFeedback: requirePermission("feedback:admin"),

  // Admin permissions
  adminUsers: requirePermission("admin:users"),
  adminSystem: requirePermission("admin:system"),
  adminSecurity: requirePermission("admin:security"),

  // Resource access
  accessOwnUser: requireResourceAccess("user", "read"),
  updateOwnUser: requireResourceAccess("user", "update"),
  accessOwnAnalytics: requireResourceAccess("analytics", "read"),
  accessOwnFeedback: requireResourceAccess("feedback", "read"),
};

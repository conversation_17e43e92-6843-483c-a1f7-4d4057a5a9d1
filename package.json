{"name": "remix-cloudflare-neon-starter", "private": true, "sideEffects": false, "type": "module", "scripts": {"build": "remix vite:build", "deploy": "wrangler deploy", "dev": "remix vite:dev", "lint": "biome lint .", "lint:fix": "biome lint --write .", "format": "biome format .", "format:fix": "biome format --write .", "check": "biome check .", "check:fix": "biome check --write .", "test": "vitest run", "start": "wrangler dev", "typecheck": "tsc", "typegen": "wrangler types", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "build:analyze": "ANALYZE=true remix vite:build", "analyze": "yarn build:analyze", "perf": "yarn build && yarn start"}, "dependencies": {"@ai-sdk/deepseek": "^0.2.14", "@ai-sdk/openai": "^1.3.22", "@ai-sdk/openai-compatible": "^0.2.14", "@ai-sdk/provider": "^1.1.3", "@ai-sdk/provider-utils": "^2.2.8", "@ai-sdk/replicate": "^0.2.8", "@aws-sdk/client-s3": "^3.454.0", "@aws-sdk/s3-request-presigner": "^3.828.0", "@keystatic/core": "^0.5.47", "@keystatic/remix": "^5.0.3", "@neondatabase/serverless": "^1.0.1", "@openrouter/ai-sdk-provider": "^0.7.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@react-email/render": "^1.1.2", "@remix-run/cloudflare": "^2.16.8", "@remix-run/react": "^2.16.8", "@remix-run/server-runtime": "^2.16.8", "@stackframe/react": "^2.8.12", "@stripe/react-stripe-js": "^2.7.3", "@stripe/stripe-js": "^4.1.0", "ai": "^4.3.16", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cookie": "^1.0.2", "drizzle-orm": "^0.44.2", "framer-motion": "^12.18.1", "google-one-tap": "^1.0.6", "isbot": "^5.1.28", "lucide-react": "^0.515.0", "marked": "^15.0.12", "next-auth": "^4.24.5", "openai": "^5.3.0", "react": "^18.3.1", "react-dom": "^18.3.1", "resend": "^3.4.0", "sonner": "^2.0.5", "stripe": "^16.2.0", "tailwind-merge": "^3.3.1", "zod": "^3.25.64", "zustand": "^5.0.5"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@cloudflare/workers-types": "^4.20250614.0", "@remix-run/dev": "^2.16.8", "@tailwindcss/postcss": "^4.1.10", "@testing-library/jest-dom": "^6.4.8", "@testing-library/react": "^16.0.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "autoprefixer": "^10.4.19", "drizzle-kit": "^0.31.1", "happy-dom": "^14.12.3", "postcss": "^8.4.38", "shadcn": "^2.6.3", "tailwindcss": "^4.1.10", "typescript": "^5.1.6", "vite": "^6.0.0", "vite-bundle-analyzer": "^0.22.2", "vite-tsconfig-paths": "^5.1.4", "vitest": "^2.0.4", "wrangler": "^3.114.9"}, "engines": {"node": ">=20.0.0"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}
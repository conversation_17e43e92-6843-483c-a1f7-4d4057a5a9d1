# Cloudflare R2 Storage Integration Guide

这个项目已经集成了Cloudflare R2存储功能，提供了简单易用的文件上传、下载和管理功能。

## 🚀 快速开始

### 1. 配置R2存储桶

在你的Cloudflare Dashboard中：
1. 进入 R2 Object Storage
2. 创建一个新的存储桶（如果还没有的话）
3. 记录存储桶名称

### 2. 配置wrangler.toml

项目已经预配置了R2绑定，你只需要确认配置正确：

```toml
# R2 Storage Binding
[[r2_buckets]]
binding = "R2_BUCKET"
bucket_name = "remix-starter-r2"  # 替换为你的存储桶名称
preview_bucket_name = "remix-starter-r2-preview"  # 可选：预览环境存储桶

# Environment variables
[vars]
R2_BUCKET_NAME = "remix-starter-r2"  # 替换为你的存储桶名称
R2_PUBLIC_URL = "https://your-r2-domain.com"  # 可选：自定义域名
R2_ACCOUNT_ID = "5dcbb0ef71fe63f030576821222ef9aa"  # 替换为你的账户ID
```

### 3. 部署到Cloudflare

```bash
# 构建项目
yarn build

# 部署到Cloudflare Workers
yarn deploy
```

## 📁 API 端点

### 上传文件
- **端点**: `POST /api/r2-upload`
- **格式**: `multipart/form-data`
- **字段**: `file`

```javascript
const formData = new FormData();
formData.append('file', fileInput.files[0]);

const response = await fetch('/api/r2-upload', {
  method: 'POST',
  body: formData,
});

const result = await response.json();
console.log(result.file); // { key, url, size, contentType, etag }
```

### 下载文件
- **端点**: `GET /api/r2-download?key={fileKey}`
- **参数**: 
  - `key`: 文件键（必需）
  - `disposition`: `inline` 或 `attachment`（可选）
  - `filename`: 下载文件名（可选）

```javascript
// 直接下载
window.open(`/api/r2-download?key=${encodeURIComponent(fileKey)}`);

// 强制下载
window.open(`/api/r2-download?key=${encodeURIComponent(fileKey)}&disposition=attachment&filename=myfile.pdf`);
```

### 列出文件
- **端点**: `GET /api/r2-list`
- **参数**:
  - `prefix`: 文件前缀过滤（可选）
  - `limit`: 返回文件数量限制（可选，默认100）

```javascript
const response = await fetch('/api/r2-list?prefix=uploads/&limit=50');
const result = await response.json();
console.log(result.files); // 文件列表
console.log(result.summary); // 统计信息
```

### 删除文件
- **端点**: `DELETE /api/r2-list?key={fileKey}`
- **参数**: `key`: 要删除的文件键

```javascript
const response = await fetch(`/api/r2-list?key=${encodeURIComponent(fileKey)}`, {
  method: 'DELETE',
});
const result = await response.json();
```

## 🧪 测试功能

### 1. 访问测试页面
部署后访问 `/r2-test` 页面，这个页面提供了：
- 文件上传界面
- 文件列表显示
- 文件下载和删除功能
- 配置状态检查

### 2. 运行自动化测试
```bash
# 启动开发服务器
yarn dev

# 在另一个终端运行测试
yarn test test/r2-storage.test.tsx
```

## 📝 使用示例

### React组件中使用

```tsx
import { useState } from 'react';

export function FileUploader() {
  const [uploading, setUploading] = useState(false);
  const [uploadResult, setUploadResult] = useState(null);

  const handleUpload = async (file: File) => {
    setUploading(true);
    
    const formData = new FormData();
    formData.append('file', file);

    try {
      const response = await fetch('/api/r2-upload', {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();
      if (result.success) {
        setUploadResult(result.file);
      }
    } catch (error) {
      console.error('Upload failed:', error);
    } finally {
      setUploading(false);
    }
  };

  return (
    <div>
      <input
        type="file"
        onChange={(e) => e.target.files?.[0] && handleUpload(e.target.files[0])}
        disabled={uploading}
      />
      {uploading && <p>Uploading...</p>}
      {uploadResult && (
        <div>
          <p>Upload successful!</p>
          <a href={uploadResult.url} target="_blank">Download</a>
        </div>
      )}
    </div>
  );
}
```

### 在Remix Action中使用

```tsx
import type { ActionFunctionArgs } from "@remix-run/cloudflare";

export async function action({ request, context }: ActionFunctionArgs) {
  const R2_BUCKET = context.cloudflare.env.R2_BUCKET;
  
  if (!R2_BUCKET) {
    throw new Error('R2 not configured');
  }

  const formData = await request.formData();
  const file = formData.get('file') as File;

  // 上传到R2
  const key = `uploads/${Date.now()}_${file.name}`;
  await R2_BUCKET.put(key, file, {
    httpMetadata: { contentType: file.type },
  });

  return { success: true, key };
}
```

## 🔧 配置选项

### 文件大小限制
默认最大文件大小为100MB，可以在上传API中修改：

```typescript
// app/routes/api.r2-upload.tsx
const MAX_FILE_SIZE = 100 * 1024 * 1024; // 100MB
```

### 允许的文件类型
默认允许的文件类型在测试页面配置中定义：

```typescript
allowedTypes: ["image/*", "video/*", "audio/*", "application/pdf"]
```

### 自定义域名
如果你为R2存储桶配置了自定义域名，可以在环境变量中设置：

```toml
[vars]
R2_PUBLIC_URL = "https://files.yourdomain.com"
```

## 🛠️ 故障排除

### 1. R2未配置错误
如果看到"R2 bucket not configured"错误：
- 检查wrangler.toml中的R2绑定配置
- 确认存储桶名称正确
- 重新部署应用

### 2. 上传失败
- 检查文件大小是否超过限制
- 确认文件类型是否被允许
- 查看浏览器控制台的错误信息

### 3. 下载失败
- 确认文件键正确
- 检查文件是否存在于存储桶中
- 验证R2绑定是否正常工作

## 🔐 安全考虑

1. **文件类型验证**: 在生产环境中应该添加更严格的文件类型验证
2. **文件大小限制**: 根据需要调整文件大小限制
3. **访问控制**: 考虑添加用户认证和授权
4. **恶意文件检测**: 对上传的文件进行安全扫描

## 📚 更多资源

- [Cloudflare R2 文档](https://developers.cloudflare.com/r2/)
- [Cloudflare Workers 文档](https://developers.cloudflare.com/workers/)
- [Remix 文档](https://remix.run/docs)

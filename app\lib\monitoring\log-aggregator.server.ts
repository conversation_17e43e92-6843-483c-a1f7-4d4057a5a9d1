/**
 * Log Aggregation and Query System
 * Centralized log collection, aggregation, and advanced querying capabilities
 */

export interface LogAggregator {
  collect(source: string, logs: LogEntry[]): void;
  query(query: LogQuery): LogQueryResult;
  getMetrics(timeRange: TimeRange): LogMetrics;
  createAlert(condition: AlertCondition): string;
  export(format: "json" | "csv" | "txt", query?: LogQuery): string;
}

export interface LogEntry {
  id: string;
  timestamp: Date;
  level: "error" | "warning" | "info" | "debug";
  message: string;
  source: string; // e.g., 'application', 'security', 'performance', 'database'
  component: string;
  operation?: string;
  userUuid?: string;
  requestId?: string;
  sessionId?: string;
  endpoint?: string;
  method?: string;
  statusCode?: number;
  duration?: number;
  metadata?: Record<string, any>;
  tags?: string[];
}

export interface LogQuery {
  sources?: string[];
  levels?: string[];
  components?: string[];
  timeRange?: TimeRange;
  search?: string; // Text search in message
  filters?: LogFilter[];
  sortBy?: "timestamp" | "level" | "component";
  sortOrder?: "asc" | "desc";
  limit?: number;
  offset?: number;
}

export interface LogFilter {
  field: string;
  operator: "equals" | "contains" | "startsWith" | "endsWith" | "regex" | "exists";
  value?: any;
}

export interface TimeRange {
  start: Date;
  end: Date;
}

export interface LogQueryResult {
  logs: LogEntry[];
  total: number;
  aggregations?: Record<string, any>;
  executionTime: number;
}

export interface LogMetrics {
  totalLogs: number;
  logsByLevel: Record<string, number>;
  logsBySource: Record<string, number>;
  logsByComponent: Record<string, number>;
  errorRate: number;
  topErrors: Array<{
    message: string;
    count: number;
    component: string;
  }>;
  timeline: Array<{
    timestamp: Date;
    count: number;
    errorCount: number;
  }>;
}

export interface AlertCondition {
  name: string;
  query: LogQuery;
  threshold: number;
  timeWindow: number; // minutes
  enabled: boolean;
}

export interface LogAlert {
  id: string;
  condition: AlertCondition;
  triggered: boolean;
  triggeredAt?: Date;
  count: number;
  lastCheck: Date;
}

// In-memory log storage (in production, use a proper log storage solution)
const logStorage: LogEntry[] = [];
const logAlerts: LogAlert[] = [];
const maxLogEntries = 50000; // Keep last 50k logs

/**
 * Default log aggregator implementation
 */
export class DefaultLogAggregator implements LogAggregator {
  /**
   * Collect logs from a source
   */
  collect(source: string, logs: LogEntry[]): void {
    const enrichedLogs = logs.map((log) => ({
      ...log,
      source,
      timestamp: log.timestamp || new Date(),
    }));

    logStorage.push(...enrichedLogs);

    // Keep only recent logs
    if (logStorage.length > maxLogEntries) {
      logStorage.splice(0, logStorage.length - maxLogEntries);
    }

    // Check alerts
    this.checkAlerts(enrichedLogs);

    console.log(`[LOG_AGGREGATOR] Collected ${logs.length} logs from ${source}`);
  }

  /**
   * Query logs with advanced filtering
   */
  query(query: LogQuery): LogQueryResult {
    const startTime = Date.now();
    let filtered = [...logStorage];

    // Filter by sources
    if (query.sources && query.sources.length > 0) {
      filtered = filtered.filter((log) => query.sources!.includes(log.source));
    }

    // Filter by levels
    if (query.levels && query.levels.length > 0) {
      filtered = filtered.filter((log) => query.levels!.includes(log.level));
    }

    // Filter by components
    if (query.components && query.components.length > 0) {
      filtered = filtered.filter((log) => query.components!.includes(log.component));
    }

    // Filter by time range
    if (query.timeRange) {
      filtered = filtered.filter(
        (log) => log.timestamp >= query.timeRange!.start && log.timestamp <= query.timeRange!.end
      );
    }

    // Text search
    if (query.search) {
      const searchLower = query.search.toLowerCase();
      filtered = filtered.filter(
        (log) =>
          log.message.toLowerCase().includes(searchLower) ||
          log.component.toLowerCase().includes(searchLower) ||
          (log.operation && log.operation.toLowerCase().includes(searchLower))
      );
    }

    // Apply custom filters
    if (query.filters) {
      for (const filter of query.filters) {
        filtered = this.applyFilter(filtered, filter);
      }
    }

    // Sort results
    const sortBy = query.sortBy || "timestamp";
    const sortOrder = query.sortOrder || "desc";

    filtered.sort((a, b) => {
      let aValue: any, bValue: any;

      switch (sortBy) {
        case "timestamp":
          aValue = a.timestamp.getTime();
          bValue = b.timestamp.getTime();
          break;
        case "level":
          const levelOrder = { error: 4, warning: 3, info: 2, debug: 1 };
          aValue = levelOrder[a.level as keyof typeof levelOrder] || 0;
          bValue = levelOrder[b.level as keyof typeof levelOrder] || 0;
          break;
        case "component":
          aValue = a.component;
          bValue = b.component;
          break;
        default:
          aValue = a.timestamp.getTime();
          bValue = b.timestamp.getTime();
      }

      if (sortOrder === "asc") {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    const total = filtered.length;

    // Apply pagination
    const offset = query.offset || 0;
    const limit = query.limit || 100;
    const paginatedLogs = filtered.slice(offset, offset + limit);

    const executionTime = Date.now() - startTime;

    return {
      logs: paginatedLogs,
      total,
      executionTime,
    };
  }

  /**
   * Apply a single filter to logs
   */
  private applyFilter(logs: LogEntry[], filter: LogFilter): LogEntry[] {
    return logs.filter((log) => {
      const fieldValue = this.getFieldValue(log, filter.field);

      if (fieldValue === undefined || fieldValue === null) {
        return filter.operator === "exists" ? false : true;
      }

      const stringValue = String(fieldValue).toLowerCase();
      const filterValue = filter.value ? String(filter.value).toLowerCase() : "";

      switch (filter.operator) {
        case "equals":
          return stringValue === filterValue;
        case "contains":
          return stringValue.includes(filterValue);
        case "startsWith":
          return stringValue.startsWith(filterValue);
        case "endsWith":
          return stringValue.endsWith(filterValue);
        case "regex":
          try {
            const regex = new RegExp(filter.value, "i");
            return regex.test(stringValue);
          } catch {
            return false;
          }
        case "exists":
          return true;
        default:
          return true;
      }
    });
  }

  /**
   * Get field value from log entry
   */
  private getFieldValue(log: LogEntry, field: string): any {
    const parts = field.split(".");
    let value: any = log;

    for (const part of parts) {
      if (value && typeof value === "object") {
        value = value[part];
      } else {
        return undefined;
      }
    }

    return value;
  }

  /**
   * Get log metrics for a time range
   */
  getMetrics(timeRange: TimeRange): LogMetrics {
    const filtered = logStorage.filter(
      (log) => log.timestamp >= timeRange.start && log.timestamp <= timeRange.end
    );

    // Count by level
    const logsByLevel: Record<string, number> = {};
    filtered.forEach((log) => {
      logsByLevel[log.level] = (logsByLevel[log.level] || 0) + 1;
    });

    // Count by source
    const logsBySource: Record<string, number> = {};
    filtered.forEach((log) => {
      logsBySource[log.source] = (logsBySource[log.source] || 0) + 1;
    });

    // Count by component
    const logsByComponent: Record<string, number> = {};
    filtered.forEach((log) => {
      logsByComponent[log.component] = (logsByComponent[log.component] || 0) + 1;
    });

    // Calculate error rate
    const totalLogs = filtered.length;
    const errorLogs = logsByLevel.error || 0;
    const errorRate = totalLogs > 0 ? (errorLogs / totalLogs) * 100 : 0;

    // Get top errors
    const errorMessages: Record<string, { count: number; component: string }> = {};
    filtered
      .filter((log) => log.level === "error")
      .forEach((log) => {
        const key = log.message.substring(0, 100); // Truncate for grouping
        if (!errorMessages[key]) {
          errorMessages[key] = { count: 0, component: log.component };
        }
        errorMessages[key].count++;
      });

    const topErrors = Object.entries(errorMessages)
      .sort(([, a], [, b]) => b.count - a.count)
      .slice(0, 10)
      .map(([message, data]) => ({
        message,
        count: data.count,
        component: data.component,
      }));

    // Create timeline (hourly buckets)
    const timeline: Array<{ timestamp: Date; count: number; errorCount: number }> = [];
    const timeSpan = timeRange.end.getTime() - timeRange.start.getTime();
    const bucketSize = Math.max(60 * 60 * 1000, timeSpan / 24); // At least 1 hour, max 24 buckets

    for (let time = timeRange.start.getTime(); time < timeRange.end.getTime(); time += bucketSize) {
      const bucketStart = new Date(time);
      const bucketEnd = new Date(time + bucketSize);

      const bucketLogs = filtered.filter(
        (log) => log.timestamp >= bucketStart && log.timestamp < bucketEnd
      );

      timeline.push({
        timestamp: bucketStart,
        count: bucketLogs.length,
        errorCount: bucketLogs.filter((log) => log.level === "error").length,
      });
    }

    return {
      totalLogs,
      logsByLevel,
      logsBySource,
      logsByComponent,
      errorRate,
      topErrors,
      timeline,
    };
  }

  /**
   * Create a log alert
   */
  createAlert(condition: AlertCondition): string {
    const alert: LogAlert = {
      id: generateAlertId(),
      condition,
      triggered: false,
      count: 0,
      lastCheck: new Date(),
    };

    logAlerts.push(alert);
    console.log(`[LOG_AGGREGATOR] Created alert: ${condition.name}`);

    return alert.id;
  }

  /**
   * Check alerts against new logs
   */
  private checkAlerts(newLogs: LogEntry[]): void {
    const now = new Date();

    for (const alert of logAlerts) {
      if (!alert.condition.enabled) continue;

      // Create time range for alert window
      const windowStart = new Date(now.getTime() - alert.condition.timeWindow * 60 * 1000);
      const timeRange = { start: windowStart, end: now };

      // Query logs for alert condition
      const query: LogQuery = {
        ...alert.condition.query,
        timeRange,
      };

      const result = this.query(query);
      alert.count = result.total;
      alert.lastCheck = now;

      // Check if threshold is exceeded
      if (result.total >= alert.condition.threshold && !alert.triggered) {
        alert.triggered = true;
        alert.triggeredAt = now;

        console.warn(
          `[LOG_AGGREGATOR] ALERT TRIGGERED: ${alert.condition.name} (${result.total} >= ${alert.condition.threshold})`
        );

        // TODO: Send alert notification
      } else if (result.total < alert.condition.threshold && alert.triggered) {
        alert.triggered = false;
        console.log(`[LOG_AGGREGATOR] Alert resolved: ${alert.condition.name}`);
      }
    }
  }

  /**
   * Export logs in various formats
   */
  export(format: "json" | "csv" | "txt", query?: LogQuery): string {
    const result = query
      ? this.query(query)
      : { logs: logStorage, total: logStorage.length, executionTime: 0 };

    switch (format) {
      case "json":
        return JSON.stringify(result.logs, null, 2);

      case "csv":
        if (result.logs.length === 0) return "";

        const headers = [
          "timestamp",
          "level",
          "source",
          "component",
          "message",
          "userUuid",
          "endpoint",
        ];
        const csvRows = [headers.join(",")];

        result.logs.forEach((log) => {
          const row = headers.map((header) => {
            const value = log[header as keyof LogEntry];
            const stringValue = value instanceof Date ? value.toISOString() : String(value || "");
            return `"${stringValue.replace(/"/g, '""')}"`;
          });
          csvRows.push(row.join(","));
        });

        return csvRows.join("\n");

      case "txt":
        return result.logs
          .map(
            (log) =>
              `[${log.timestamp.toISOString()}] [${log.level.toUpperCase()}] [${log.source}/${log.component}] ${log.message}`
          )
          .join("\n");

      default:
        return JSON.stringify(result.logs);
    }
  }
}

/**
 * Generate unique alert ID
 */
function generateAlertId(): string {
  return `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Get active log alerts
 */
export function getLogAlerts(): LogAlert[] {
  return [...logAlerts];
}

/**
 * Get log storage statistics
 */
export function getLogStorageStats(): {
  totalLogs: number;
  oldestLog?: Date;
  newestLog?: Date;
  sources: string[];
  components: string[];
} {
  if (logStorage.length === 0) {
    return {
      totalLogs: 0,
      sources: [],
      components: [],
    };
  }

  const sources = [...new Set(logStorage.map((log) => log.source))];
  const components = [...new Set(logStorage.map((log) => log.component))];
  const timestamps = logStorage.map((log) => log.timestamp.getTime());

  return {
    totalLogs: logStorage.length,
    oldestLog: new Date(Math.min(...timestamps)),
    newestLog: new Date(Math.max(...timestamps)),
    sources,
    components,
  };
}

/**
 * Clear log storage
 */
export function clearLogStorage(): number {
  const count = logStorage.length;
  logStorage.length = 0;
  console.log(`[LOG_AGGREGATOR] Cleared ${count} logs from storage`);
  return count;
}

// Create default log aggregator instance
export const logAggregator = new DefaultLogAggregator();

// Helper functions for common log queries
export const commonQueries = {
  errors: (timeRange: TimeRange): LogQuery => ({
    levels: ["error"],
    timeRange,
    sortBy: "timestamp",
    sortOrder: "desc",
  }),

  warnings: (timeRange: TimeRange): LogQuery => ({
    levels: ["warning"],
    timeRange,
    sortBy: "timestamp",
    sortOrder: "desc",
  }),

  byComponent: (component: string, timeRange: TimeRange): LogQuery => ({
    components: [component],
    timeRange,
    sortBy: "timestamp",
    sortOrder: "desc",
  }),

  byUser: (userUuid: string, timeRange: TimeRange): LogQuery => ({
    filters: [{ field: "userUuid", operator: "equals", value: userUuid }],
    timeRange,
    sortBy: "timestamp",
    sortOrder: "desc",
  }),

  slowRequests: (threshold: number, timeRange: TimeRange): LogQuery => ({
    filters: [{ field: "duration", operator: "exists" }],
    timeRange,
    sortBy: "timestamp",
    sortOrder: "desc",
  }),
};

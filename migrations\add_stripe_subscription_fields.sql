-- Migration: Add Stripe integration fields to subscription tables
-- This migration adds necessary fields for Stripe subscription management

-- Add Stripe fields to subscriptions table
ALTER TABLE subscriptions 
ADD COLUMN IF NOT EXISTS stripe_subscription_id TEXT UNIQUE,
ADD COLUMN IF NOT EXISTS stripe_customer_id TEXT;

-- Make billing_customer_id optional (for Stripe-only subscriptions)
ALTER TABLE subscriptions 
ALTER COLUMN billing_customer_id DROP NOT NULL;

-- Add Stripe fields to subscription_items table
ALTER TABLE subscription_items 
ADD COLUMN IF NOT EXISTS stripe_subscription_item_id TEXT UNIQUE,
ADD COLUMN IF NOT EXISTS stripe_price_id TEXT;

-- Make variant_id optional
ALTER TABLE subscription_items 
ALTER COLUMN variant_id DROP NOT NULL;

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_subscriptions_stripe_subscription_id 
ON subscriptions(stripe_subscription_id);

CREATE INDEX IF NOT EXISTS idx_subscriptions_stripe_customer_id 
ON subscriptions(stripe_customer_id);

CREATE INDEX IF NOT EXISTS idx_subscription_items_stripe_subscription_item_id 
ON subscription_items(stripe_subscription_item_id);

CREATE INDEX IF NOT EXISTS idx_subscription_items_stripe_price_id 
ON subscription_items(stripe_price_id);

-- Add comments for documentation
COMMENT ON COLUMN subscriptions.stripe_subscription_id IS 'Stripe subscription ID for webhook synchronization';
COMMENT ON COLUMN subscriptions.stripe_customer_id IS 'Stripe customer ID for billing management';
COMMENT ON COLUMN subscription_items.stripe_subscription_item_id IS 'Stripe subscription item ID';
COMMENT ON COLUMN subscription_items.stripe_price_id IS 'Stripe price ID for the subscription item';

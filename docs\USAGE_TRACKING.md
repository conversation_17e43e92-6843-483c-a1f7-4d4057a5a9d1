# API Usage Tracking System

## Overview

The API Usage Tracking System provides comprehensive monitoring and analytics for all API calls, enabling detailed insights into usage patterns, costs, and performance metrics. This is essential for SAAS billing, rate limiting, and business analytics.

## Features

### ✅ Implemented Features

1. **Comprehensive API Tracking**
   - All API calls automatically tracked
   - Request/response metadata capture
   - Duration and performance metrics
   - Success/failure status tracking

2. **Token and Credit Monitoring**
   - AI token consumption tracking
   - Credit usage and deduction logging
   - Cost estimation and analysis
   - Provider-specific usage breakdown

3. **Analytics Dashboard**
   - Real-time usage statistics
   - Historical usage trends
   - Provider and model breakdowns
   - Success rate monitoring

4. **Rate Limiting Infrastructure**
   - Per-endpoint rate limits
   - Token and credit quotas
   - Automatic blocking mechanisms
   - Rate limit headers

## Database Schema

### Core Tables

#### `api_usage` - Detailed Request Tracking
```sql
- id: UUID (Primary Key)
- user_uuid: UUID (Foreign Key to users)
- endpoint: VARCHAR(100) - API endpoint called
- method: VARCHAR(10) - HTTP method
- provider: VARCHAR(50) - AI provider (if applicable)
- model: VARCHAR(100) - AI model used (if applicable)
- request_size: INTEGER - Request payload size in bytes
- response_size: INTEGER - Response payload size in bytes
- tokens_used: INTEGER - Tokens consumed
- credits_used: INTEGER - Credits deducted
- duration: INTEGER - Request duration in milliseconds
- status: VARCHAR(20) - success, error, timeout
- error_code: VARCHAR(50) - Error code if failed
- error_message: TEXT - Error message if failed
- ip_address: VARCHAR(45) - Client IP address
- user_agent: TEXT - Client user agent
- metadata: JSONB - Additional metadata
- created_at: TIMESTAMPTZ - Request timestamp
```

#### `usage_stats` - Aggregated Statistics
```sql
- id: UUID (Primary Key)
- user_uuid: UUID (Foreign Key to users)
- period: VARCHAR(20) - daily, weekly, monthly
- period_start: TIMESTAMPTZ - Period start time
- period_end: TIMESTAMPTZ - Period end time
- total_requests: INTEGER - Total requests in period
- successful_requests: INTEGER - Successful requests
- failed_requests: INTEGER - Failed requests
- total_tokens: INTEGER - Total tokens consumed
- total_credits: INTEGER - Total credits used
- total_cost: DECIMAL(10,4) - Estimated cost in USD
- avg_response_time: INTEGER - Average response time
- top_provider: VARCHAR(50) - Most used provider
- top_model: VARCHAR(100) - Most used model
- top_endpoint: VARCHAR(100) - Most used endpoint
- metadata: JSONB - Additional statistics
```

#### `rate_limits` - Rate Limiting Data
```sql
- id: UUID (Primary Key)
- user_uuid: UUID (Foreign Key to users)
- endpoint: VARCHAR(100) - Rate limited endpoint
- window_start: TIMESTAMPTZ - Rate limit window start
- window_end: TIMESTAMPTZ - Rate limit window end
- request_count: INTEGER - Requests in window
- token_count: INTEGER - Tokens in window
- credit_count: INTEGER - Credits in window
- is_blocked: BOOLEAN - Whether user is blocked
- blocked_until: TIMESTAMPTZ - Block expiration time
```

## File Structure

```
app/
├── services/
│   └── usage-tracking.server.ts     # Core tracking service
├── components/dashboard/
│   └── usage-analytics.tsx          # Analytics dashboard
├── routes/
│   ├── api.usage.analytics.tsx      # Analytics API endpoint
│   └── test.usage-tracking.tsx      # Development test page
├── lib/middleware/
│   └── usage-tracking.server.ts     # Tracking middleware (optional)
└── migrations/
    └── add_usage_tracking_tables.sql # Database migration
```

## Usage

### Automatic Tracking

Usage tracking is automatically integrated into AI API routes:

```typescript
// In API routes (e.g., api.ai.generate-text.tsx)
import { trackApiUsage } from "~/services/usage-tracking.server";

// Track successful usage
await trackApiUsage({
  userUuid,
  endpoint: "/api/ai/generate-text",
  method: "POST",
  provider,
  model,
  tokensUsed: result.usage?.totalTokens || 0,
  creditsUsed: CreditsAmount.AITextGenerationCost,
  duration: Date.now() - startTime,
  status: "success",
  metadata: { finishReason: result.finishReason },
}, db);
```

### Analytics API

Get user usage analytics:

```typescript
// GET /api/usage/analytics?period=7&recent=true
const response = await fetch('/api/usage/analytics?period=7&recent=true');
const data = await response.json();

// Returns:
{
  success: true,
  data: {
    analytics: {
      totalRequests: 150,
      successfulRequests: 145,
      failedRequests: 5,
      totalTokens: 25000,
      totalCredits: 500,
      avgResponseTime: 1250,
      requestsByProvider: { "openai": 100, "deepseek": 50 },
      dailyUsage: [...]
    },
    recentUsage: [...],
    period: { days: 7, startDate: "...", endDate: "..." }
  }
}
```

### Dashboard Component

```tsx
import UsageAnalytics from "~/components/dashboard/usage-analytics";

function Dashboard() {
  return (
    <div>
      <h1>Usage Dashboard</h1>
      <UsageAnalytics />
    </div>
  );
}
```

## Rate Limiting

### Configuration

Rate limits are configured per endpoint:

```typescript
const RATE_LIMIT_CONFIGS = {
  "/api/ai/generate-text": {
    endpoint: "/api/ai/generate-text",
    windowMinutes: 60,
    maxRequests: 100,
    maxTokens: 50000,
    maxCredits: 500,
  },
  // ... other endpoints
};
```

### Checking Limits

```typescript
import { checkRateLimit } from "~/services/usage-tracking.server";

const rateLimitResult = await checkRateLimit(
  userUuid,
  endpoint,
  config,
  db
);

if (!rateLimitResult.allowed) {
  return new Response("Rate limit exceeded", { status: 429 });
}
```

## Analytics Features

### Overview Metrics
- Total requests and success rate
- Token and credit consumption
- Average response times
- Cost estimation

### Breakdown Analysis
- Usage by AI provider
- Usage by AI model
- Usage by endpoint
- Status code distribution

### Time-based Analytics
- Daily usage trends
- Period comparisons
- Peak usage identification
- Growth tracking

### Recent Activity
- Last 20 API calls
- Real-time status monitoring
- Error tracking and debugging

## Cost Calculation

The system includes a basic cost calculation model:

```typescript
export function calculateUsageCost(
  provider: string,
  model: string,
  tokensUsed: number,
  operation: "text" | "image" | "embedding" = "text"
): number {
  const pricing = {
    openai: {
      "gpt-4o": 0.00003, // per token
      "dall-e-3": 0.04,  // per image
    },
    // ... other providers
  };
  
  return tokensUsed * pricing[provider][model];
}
```

## Testing

### Test Page

Visit `/test/usage-tracking` to:
- Run test API calls
- Generate sample usage data
- View real-time analytics
- Test rate limiting

### Test Commands

```bash
# Run individual tests
curl -X POST /api/ai/generate-text \
  -H "Content-Type: application/json" \
  -d '{"prompt":"test","provider":"openai","model":"gpt-3.5-turbo"}'

# Check analytics
curl /api/usage/analytics?period=1&recent=true
```

## Performance Considerations

### Indexing Strategy
- User-based queries: `(user_uuid, created_at)`
- Endpoint analysis: `(endpoint, created_at)`
- Provider analysis: `(provider, created_at)`
- Time-based queries: `(created_at)`

### Data Retention
- Raw usage data: 90 days
- Aggregated stats: 2 years
- Rate limit data: 7 days

### Optimization Tips
1. Use aggregated stats for historical analysis
2. Implement data archiving for old records
3. Consider read replicas for analytics queries
4. Cache frequently accessed metrics

## Security and Privacy

### Data Protection
- IP addresses are hashed for privacy
- User agents are truncated
- Sensitive metadata is filtered
- GDPR compliance considerations

### Access Control
- Users can only access their own data
- Admin users can access aggregate data
- API keys required for programmatic access

## Monitoring and Alerts

### Key Metrics to Monitor
- API response times
- Error rates by endpoint
- Rate limit violations
- Unusual usage patterns

### Alert Conditions
- Error rate > 5%
- Response time > 5 seconds
- Rate limit violations
- Suspicious usage patterns

## Future Enhancements

- [ ] Real-time streaming analytics
- [ ] Advanced cost optimization recommendations
- [ ] Predictive usage forecasting
- [ ] Custom alert configurations
- [ ] Export functionality for external analysis
- [ ] Integration with business intelligence tools
- [ ] Advanced rate limiting strategies
- [ ] Usage-based billing automation

import type { EmailTemplate } from "../templating.server";
import { welcomeTemplate } from "./welcome";
import { passwordResetTemplate } from "./password-reset";
import { notificationTemplate } from "./notification";
import { paymentSuccessTemplate } from "./payment-success";

// Re-export templates for convenience
export { welcomeTemplate, passwordResetTemplate, notificationTemplate, paymentSuccessTemplate };

export type TemplateName = "welcome" | "passwordReset" | "notification" | "payment-success";

export const templates: Record<TemplateName, EmailTemplate<any>> = {
  welcome: welcomeTemplate,
  passwordReset: passwordResetTemplate,
  notification: notificationTemplate,
  "payment-success": paymentSuccessTemplate,
};

export const getTemplate = (name: TemplateName): EmailTemplate<any> | undefined => {
  return templates[name];
};

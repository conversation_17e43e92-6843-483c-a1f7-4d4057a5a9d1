/**
 * R2 Storage Tests
 * 测试Cloudflare R2存储功能
 */

import { beforeAll, describe, expect, it } from "vitest";

// 模拟文件数据
const createMockFile = (name: string, content: string, type = "text/plain") => {
  const blob = new Blob([content], { type });
  return new File([blob], name, { type });
};

// 模拟base64图片数据 (1x1像素的红色PNG)
const mockImageBase64 =
  "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAGA60e6kgAAAABJRU5ErkJggg==";

describe("R2 Storage API Tests", () => {
  const baseUrl = "http://localhost:8787"; // Cloudflare Workers dev server

  beforeAll(() => {
    console.log("🧪 开始R2存储功能测试...");
  });

  describe("Upload API Tests", () => {
    it("应该能够上传文本文件", async () => {
      const file = createMockFile("test.txt", "Hello R2 Storage!", "text/plain");
      const formData = new FormData();
      formData.append("file", file);

      try {
        const response = await fetch(`${baseUrl}/api/r2-upload`, {
          method: "POST",
          body: formData,
        });

        const result = await response.json();
        console.log("📤 文本文件上传结果:", result);

        expect(response.ok).toBe(true);
        expect(result.success).toBe(true);
        expect(result.file).toBeDefined();
        expect(result.file.key).toContain("uploads/");
        expect(result.file.url).toContain("/api/r2-download");
      } catch (error) {
        console.error("❌ 文本文件上传失败:", error);
        throw error;
      }
    });

    it("应该能够上传图片文件", async () => {
      const imageBuffer = Buffer.from(mockImageBase64, "base64");
      const file = new File([imageBuffer], "test.png", { type: "image/png" });
      const formData = new FormData();
      formData.append("file", file);

      try {
        const response = await fetch(`${baseUrl}/api/r2-upload`, {
          method: "POST",
          body: formData,
        });

        const result = await response.json();
        console.log("📤 图片文件上传结果:", result);

        expect(response.ok).toBe(true);
        expect(result.success).toBe(true);
        expect(result.file).toBeDefined();
        expect(result.file.contentType).toBe("image/png");
      } catch (error) {
        console.error("❌ 图片文件上传失败:", error);
        throw error;
      }
    });

    it("应该拒绝没有文件的请求", async () => {
      const formData = new FormData();

      try {
        const response = await fetch(`${baseUrl}/api/r2-upload`, {
          method: "POST",
          body: formData,
        });

        const result = await response.json();
        console.log("📤 空文件上传结果:", result);

        expect(response.ok).toBe(false);
        expect(result.error).toBeDefined();
      } catch (error) {
        console.error("❌ 空文件上传测试失败:", error);
        throw error;
      }
    });
  });

  describe("Download API Tests", () => {
    let uploadedFileKey: string;

    beforeAll(async () => {
      // 先上传一个测试文件
      const file = createMockFile("download-test.txt", "Test download content", "text/plain");
      const formData = new FormData();
      formData.append("file", file);

      const response = await fetch(`${baseUrl}/api/r2-upload`, {
        method: "POST",
        body: formData,
      });

      const result = await response.json();
      uploadedFileKey = result.file.key;
      console.log("📁 准备下载测试文件:", uploadedFileKey);
    });

    it("应该能够下载已上传的文件", async () => {
      try {
        const response = await fetch(
          `${baseUrl}/api/r2-download?key=${encodeURIComponent(uploadedFileKey)}`
        );

        console.log("📥 文件下载状态:", response.status);
        console.log("📥 文件下载头信息:", Object.fromEntries(response.headers.entries()));

        expect(response.ok).toBe(true);
        expect(response.headers.get("content-type")).toBe("text/plain");

        const content = await response.text();
        expect(content).toBe("Test download content");
      } catch (error) {
        console.error("❌ 文件下载失败:", error);
        throw error;
      }
    });

    it("应该返回404对于不存在的文件", async () => {
      try {
        const response = await fetch(`${baseUrl}/api/r2-download?key=nonexistent-file.txt`);

        console.log("📥 不存在文件下载状态:", response.status);

        expect(response.status).toBe(404);
      } catch (error) {
        console.error("❌ 不存在文件下载测试失败:", error);
        throw error;
      }
    });
  });

  describe("List API Tests", () => {
    it("应该能够列出存储桶中的文件", async () => {
      try {
        const response = await fetch(`${baseUrl}/api/r2-list`);

        const result = await response.json();
        console.log("📋 文件列表结果:", result);

        expect(response.ok).toBe(true);
        expect(result.success).toBe(true);
        expect(Array.isArray(result.files)).toBe(true);
        expect(result.summary).toBeDefined();
        expect(result.summary.totalFiles).toBeGreaterThanOrEqual(0);
      } catch (error) {
        console.error("❌ 文件列表获取失败:", error);
        throw error;
      }
    });

    it("应该能够按前缀过滤文件", async () => {
      try {
        const response = await fetch(`${baseUrl}/api/r2-list?prefix=uploads/`);

        const result = await response.json();
        console.log("📋 按前缀过滤的文件列表:", result);

        expect(response.ok).toBe(true);
        expect(result.success).toBe(true);

        // 所有文件都应该以uploads/开头
        result.files.forEach((file: any) => {
          expect(file.key).toMatch(/^uploads\//);
        });
      } catch (error) {
        console.error("❌ 按前缀过滤文件失败:", error);
        throw error;
      }
    });
  });

  describe("Configuration API Tests", () => {
    it("应该能够获取上传配置", async () => {
      try {
        const response = await fetch(`${baseUrl}/api/r2-upload`);

        const result = await response.json();
        console.log("⚙️ 上传配置:", result);

        expect(response.ok).toBe(true);
        expect(result.configured).toBeDefined();
        expect(result.maxFileSize).toBeDefined();
        expect(result.allowedTypes).toBeDefined();
        expect(result.endpoints).toBeDefined();
      } catch (error) {
        console.error("❌ 获取上传配置失败:", error);
        throw error;
      }
    });
  });

  describe("Error Handling Tests", () => {
    it("应该正确处理无效的HTTP方法", async () => {
      try {
        const response = await fetch(`${baseUrl}/api/r2-upload`, {
          method: "GET",
        });

        // 注意：GET请求应该返回配置信息，不是错误
        expect(response.ok).toBe(true);
      } catch (error) {
        console.error("❌ HTTP方法测试失败:", error);
        throw error;
      }
    });

    it("应该正确处理缺少参数的下载请求", async () => {
      try {
        const response = await fetch(`${baseUrl}/api/r2-download`);

        expect(response.status).toBe(400);

        const text = await response.text();
        expect(text).toContain("Missing file key parameter");
      } catch (error) {
        console.error("❌ 缺少参数测试失败:", error);
        throw error;
      }
    });
  });
});

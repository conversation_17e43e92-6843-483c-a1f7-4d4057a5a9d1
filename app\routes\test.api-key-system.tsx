/**
 * API Key System Test Page
 * Demonstrates API key functionality and provides testing interface
 */

import { <PERSON> } from "@remix-run/react";
import {
  <PERSON>ertTriangle,
  Book,
  CheckCircle,
  Code,
  Copy,
  ExternalLink,
  Eye,
  EyeOff,
  Globe,
  Info,
  Key,
  Lock,
  Settings,
  Shield,
  Terminal,
  XCircle,
  Zap,
} from "lucide-react";
import { useState } from "react";
import { Badge } from "~/components/ui/badge";
import { But<PERSON> } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Textarea } from "~/components/ui/textarea";

export default function ApiKeySystemTest() {
  const [showApiKey, setShowApiKey] = useState(false);
  const [testApiKey, setTestApiKey] = useState("sk-1234567890abcdef1234567890abcdef12345678");

  const features = [
    {
      name: "API Key Generation",
      description: "Secure generation of unique API keys with cryptographic randomness",
      status: "completed",
      icon: Key,
      features: [
        "✅ Cryptographically secure key generation",
        "✅ Unique key validation",
        "✅ Configurable expiration dates",
        "✅ User-friendly titles and descriptions",
        "✅ Maximum key limits per user (10 keys)",
      ],
    },
    {
      name: "Authentication & Authorization",
      description: "Robust authentication system with multiple auth methods",
      status: "completed",
      icon: Shield,
      features: [
        "✅ Multiple authentication methods (Bearer, X-API-Key, query param)",
        "✅ API key validation and status checking",
        "✅ Expiration date enforcement",
        "✅ User context extraction",
        "✅ Session + API key dual authentication",
      ],
    },
    {
      name: "Key Management Interface",
      description: "User-friendly interface for managing API keys",
      status: "completed",
      icon: Settings,
      features: [
        "✅ Create new API keys with custom titles",
        "✅ View and manage existing keys",
        "✅ Revoke and delete keys",
        "✅ Edit key titles",
        "✅ Show/hide key values for security",
        "✅ Copy keys to clipboard",
      ],
    },
    {
      name: "Security Features",
      description: "Enterprise-grade security and monitoring",
      status: "completed",
      icon: Lock,
      features: [
        "✅ Secure key storage and validation",
        "✅ Last used timestamp tracking",
        "✅ Key status management (active/revoked)",
        "✅ Usage logging and monitoring",
        "✅ Rate limiting preparation",
        "✅ Security best practices documentation",
      ],
    },
  ];

  const apiEndpoints = [
    {
      method: "GET",
      endpoint: "/api/keys?action=list",
      description: "List user's API keys with pagination",
      example:
        "curl -H 'Authorization: Bearer YOUR_SESSION_TOKEN' '/api/keys?action=list&page=1&limit=10'",
    },
    {
      method: "POST",
      endpoint: "/api/keys",
      description: "Create a new API key",
      example: `curl -X POST -H 'Content-Type: application/json' \\
  -H 'Authorization: Bearer YOUR_SESSION_TOKEN' \\
  -d '{"action":"create","title":"My API Key","expiresAt":"2024-12-31T23:59:59Z"}' \\
  '/api/keys'`,
    },
    {
      method: "POST",
      endpoint: "/api/keys",
      description: "Revoke an API key",
      example: `curl -X POST -H 'Content-Type: application/json' \\
  -H 'Authorization: Bearer YOUR_SESSION_TOKEN' \\
  -d '{"action":"revoke","apiKeyId":123}' \\
  '/api/keys'`,
    },
    {
      method: "POST",
      endpoint: "/api/ai/generate-text",
      description: "Use API key for AI text generation",
      example: `curl -X POST -H 'Content-Type: application/json' \\
  -H 'Authorization: Bearer sk-your-api-key-here' \\
  -d '{"prompt":"Hello, world!","provider":"openai","model":"gpt-4o-mini"}' \\
  '/api/ai/generate-text'`,
    },
  ];

  const authMethods = [
    {
      name: "Authorization Header (Recommended)",
      description: "Pass API key as Bearer token in Authorization header",
      example: "Authorization: Bearer sk-your-api-key-here",
      security: "High",
      color: "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",
    },
    {
      name: "X-API-Key Header",
      description: "Pass API key in custom X-API-Key header",
      example: "X-API-Key: sk-your-api-key-here",
      security: "High",
      color: "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",
    },
    {
      name: "Query Parameter",
      description: "Pass API key as query parameter (less secure)",
      example: "?api_key=sk-your-api-key-here",
      security: "Medium",
      color: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400",
    },
  ];

  const testScenarios = [
    {
      name: "API Key Creation",
      description: "Test creating new API keys with different configurations",
      steps: [
        "1. Navigate to API Keys management page",
        "2. Click 'Create API Key' button",
        "3. Enter a descriptive title",
        "4. Optionally set expiration date",
        "5. Submit form and copy the generated key",
        "6. Verify key appears in the list",
      ],
      icon: Key,
    },
    {
      name: "API Authentication",
      description: "Test API key authentication with different methods",
      steps: [
        "1. Use generated API key with Authorization header",
        "2. Test X-API-Key header method",
        "3. Try query parameter method",
        "4. Verify authentication works for AI endpoints",
        "5. Test with invalid/expired keys",
        "6. Check error handling and responses",
      ],
      icon: Shield,
    },
    {
      name: "Key Management",
      description: "Test key lifecycle management operations",
      steps: [
        "1. Create multiple API keys with different titles",
        "2. Edit key titles and verify updates",
        "3. Test show/hide key functionality",
        "4. Copy keys to clipboard",
        "5. Revoke a key and test it becomes inactive",
        "6. Delete keys and verify removal",
      ],
      icon: Settings,
    },
    {
      name: "Security Testing",
      description: "Test security features and edge cases",
      steps: [
        "1. Test with malformed API keys",
        "2. Try accessing other users' keys",
        "3. Test expired key handling",
        "4. Verify rate limiting (when implemented)",
        "5. Check usage tracking and logging",
        "6. Test key validation edge cases",
      ],
      icon: Lock,
    },
  ];

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      // TODO: Show toast notification
    } catch (error) {
      console.error("Failed to copy to clipboard:", error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";
      case "in-progress":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-4">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            API Key System Test Suite
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            Test and explore the API key management and authentication system
          </p>
        </div>

        {/* Quick Navigation */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Key className="w-5 h-5" />
              <span>Quick Navigation</span>
            </CardTitle>
            <CardDescription>Direct links to API key system pages and tools</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              <Button
                asChild
                variant="outline"
                className="h-auto p-4 flex flex-col items-center space-y-2"
              >
                <Link to="/console/api-keys">
                  <Key className="w-6 h-6" />
                  <span className="text-sm font-medium text-center">API Keys</span>
                </Link>
              </Button>
              <Button
                asChild
                variant="outline"
                className="h-auto p-4 flex flex-col items-center space-y-2"
              >
                <Link to="/api/keys?action=list">
                  <Code className="w-6 h-6" />
                  <span className="text-sm font-medium text-center">API Test</span>
                </Link>
              </Button>
              <Button
                asChild
                variant="outline"
                className="h-auto p-4 flex flex-col items-center space-y-2"
              >
                <Link to="/ai-tools">
                  <Zap className="w-6 h-6" />
                  <span className="text-sm font-medium text-center">AI Tools</span>
                </Link>
              </Button>
              <Button
                asChild
                variant="outline"
                className="h-auto p-4 flex flex-col items-center space-y-2"
              >
                <Link to="/console/usage">
                  <Globe className="w-6 h-6" />
                  <span className="text-sm font-medium text-center">Usage</span>
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Implementation Status */}
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            Implementation Status
          </h2>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {features.map((feature) => (
              <Card key={feature.name}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center space-x-2">
                      <feature.icon className="w-5 h-5" />
                      <span>{feature.name}</span>
                    </CardTitle>
                    <Badge className={getStatusColor(feature.status)}>
                      <div className="flex items-center space-x-1">
                        <CheckCircle className="w-4 h-4" />
                        <span className="capitalize">{feature.status}</span>
                      </div>
                    </Badge>
                  </div>
                  <CardDescription>{feature.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {feature.features.map((item, index) => (
                      <div key={index} className="text-sm text-gray-600 dark:text-gray-400">
                        {item}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Authentication Methods */}
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            Authentication Methods
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {authMethods.map((method) => (
              <Card key={method.name}>
                <CardHeader>
                  <CardTitle className="text-lg">{method.name}</CardTitle>
                  <CardDescription>{method.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <Badge className={method.color}>{method.security} Security</Badge>
                    <div className="bg-gray-100 dark:bg-gray-800 p-3 rounded font-mono text-sm">
                      {method.example}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* API Endpoints */}
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">API Endpoints</h2>
          <div className="space-y-4">
            {apiEndpoints.map((endpoint, index) => (
              <Card key={index}>
                <CardHeader>
                  <div className="flex items-center space-x-3">
                    <Badge variant="outline">{endpoint.method}</Badge>
                    <code className="text-sm font-mono">{endpoint.endpoint}</code>
                  </div>
                  <CardDescription>{endpoint.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-semibold">Example:</span>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => copyToClipboard(endpoint.example)}
                      >
                        <Copy className="w-4 h-4" />
                      </Button>
                    </div>
                    <pre className="text-sm overflow-x-auto">
                      <code>{endpoint.example}</code>
                    </pre>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Test Scenarios */}
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Test Scenarios</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {testScenarios.map((scenario) => (
              <Card key={scenario.name}>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <scenario.icon className="w-5 h-5" />
                    <span>{scenario.name}</span>
                  </CardTitle>
                  <CardDescription>{scenario.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {scenario.steps.map((step, index) => (
                      <div key={index} className="text-sm text-gray-600 dark:text-gray-400">
                        {step}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Testing Instructions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Terminal className="w-5 h-5" />
              <span>Testing Instructions</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                <h4 className="font-semibold text-blue-900 dark:text-blue-400 mb-2">
                  How to Test API Keys:
                </h4>
                <ol className="text-sm text-blue-800 dark:text-blue-300 space-y-1 list-decimal list-inside">
                  <li>Navigate to the API Keys management page</li>
                  <li>Create a new API key with a descriptive title</li>
                  <li>Copy the generated API key (you won't see it again)</li>
                  <li>Test the key with different authentication methods</li>
                  <li>Try using the key with AI generation endpoints</li>
                  <li>Test key management operations (edit, revoke, delete)</li>
                </ol>
              </div>

              <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                <h4 className="font-semibold text-green-900 dark:text-green-400 mb-2">
                  Security Best Practices:
                </h4>
                <ul className="text-sm text-green-800 dark:text-green-300 space-y-1 list-disc list-inside">
                  <li>Never share API keys publicly or in client-side code</li>
                  <li>Use different keys for different environments</li>
                  <li>Set expiration dates for temporary keys</li>
                  <li>Regularly rotate your API keys</li>
                  <li>Revoke compromised keys immediately</li>
                  <li>Monitor API key usage and activity</li>
                </ul>
              </div>

              <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg">
                <h4 className="font-semibold text-yellow-900 dark:text-yellow-400 mb-2">
                  Integration Examples:
                </h4>
                <ul className="text-sm text-yellow-800 dark:text-yellow-300 space-y-1 list-disc list-inside">
                  <li>Use API keys for server-to-server communication</li>
                  <li>Integrate with CI/CD pipelines for automated testing</li>
                  <li>Build custom applications using our AI services</li>
                  <li>Create webhooks and automated workflows</li>
                  <li>Develop mobile apps with secure API access</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Start Testing */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Zap className="w-5 h-5" />
              <span>Start Testing</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-3">
              <Button asChild>
                <Link to="/console/api-keys" className="flex items-center space-x-2">
                  <Key className="w-4 h-4" />
                  <span>Manage API Keys</span>
                  <ExternalLink className="w-4 h-4" />
                </Link>
              </Button>
              <Button asChild variant="outline">
                <Link to="/ai-tools" className="flex items-center space-x-2">
                  <Zap className="w-4 h-4" />
                  <span>Test AI APIs</span>
                  <ExternalLink className="w-4 h-4" />
                </Link>
              </Button>
              <Button asChild variant="outline">
                <Link to="/console/usage" className="flex items-center space-x-2">
                  <Globe className="w-4 h-4" />
                  <span>View Usage</span>
                  <ExternalLink className="w-4 h-4" />
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

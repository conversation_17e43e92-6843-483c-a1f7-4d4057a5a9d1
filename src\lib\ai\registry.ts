import { openai as vercelOpenaiSdk } from "@ai-sdk/openai";
import { replicate as vercelReplicateSdk } from "@ai-sdk/replicate";
import { type ImageModel, type LanguageModel, createProviderRegistry, customProvider } from "ai";
// import { createKlingProvider } from './kling-provider'; // Placeholder for later

// Assuming types.ts exists or will be created by this subtask
import type { AiRequestOptions, KlingProviderConfig } from "./types";

// Initialize Core Providers
const openaiProvider = vercelOpenaiSdk({
  apiKey: process.env.OPENAI_API_KEY,
  // You can add default configurations here, e.g.,
  // defaultQuery: { temperature: 0.7 },
});

const replicateProvider = vercelReplicateSdk({
  apiKey: process.env.REPLICATE_API_TOKEN,
});

// Placeholder for Kling AI provider initialization
// const klingProviderInstance = createKlingProvider({
//   accessKey: process.env.KLING_ACCESS_KEY!,
//   secretKey: process.env.KLING_SECRET_KEY!,
// } as KlingProviderConfig);

// Custom provider configurations (optional, for aliases and defaults)
const openaiCustomized = customProvider({
  languageModels: {
    "gpt-4-turbo": openaiProvider("gpt-4-turbo"),
    "gpt-3.5-turbo": openaiProvider("gpt-3.5-turbo"),
    // Add other aliases or specific model configs
  },
  imageModels: {
    "dall-e-3": openaiProvider.image("dall-e-3"),
    "dall-e-2": openaiProvider.image("dall-e-2"),
  },
  // fallbackProvider: openaiProvider, // If you want to allow any model from openaiProvider
});

// TODO: Add custom provider for Replicate if needed for model aliasing, e.g.
// const replicateCustomized = customProvider({ ... });

// Provider Registry
export const aiProviderRegistry = createProviderRegistry({
  openai: openaiCustomized,
  replicate: replicateProvider, // Using direct provider, or use replicateCustomized if defined
  // kling: klingProviderInstance, // Add when Kling provider is implemented
});

// Helper functions to safely get models
export function getLanguageModel(providerModelId: string): LanguageModel {
  try {
    return aiProviderRegistry.languageModel(providerModelId);
  } catch (error) {
    console.error(`Error getting language model '${providerModelId}':`, error);
    throw new Error(`Language model not found or configured: ${providerModelId}`);
  }
}

export function getImageModel(providerModelId: string): ImageModel {
  try {
    // Note: The AI SDK's imageModel accessor might be different or part of the provider instance directly
    // For example, openaiProvider.image('dall-e-3') is how it's often used.
    // The registry might provide a generic way, or we might need to adjust this.
    // For now, let's assume the registry has an imageModel method similar to languageModel.
    // If not, this helper will need to be provider-aware.
    const model = aiProviderRegistry.imageModel(providerModelId);
    if (!model) throw new Error("Model not found in registry.");
    return model;
  } catch (error) {
    console.error(`Error getting image model '${providerModelId}':`, error);
    throw new Error(`Image model not found or configured: ${providerModelId}`);
  }
}

// Placeholder for video model accessor if Kling is structured as a provider in the registry
// export function getVideoModel(providerModelId: string): any { // Replace 'any' with Kling's video model type
//   try {
//     const model = (aiProviderRegistry.get(providerModelId) as any)?.videoModel(); // This is speculative
//     if (!model) throw new Error('Video model not found');
//     return model;
//   } catch (error) {
//     console.error(`Error getting video model '${providerModelId}':`, error);
//     throw new Error(`Video model not found or configured: ${providerModelId}`);
//   }
// }

console.log("AI Provider Registry initialized with: openai, replicate (Kling placeholder)");

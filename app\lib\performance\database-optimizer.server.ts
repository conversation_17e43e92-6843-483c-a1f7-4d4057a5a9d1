/**
 * Database Query Optimization System
 * Provides query caching, connection pooling, and performance monitoring
 */

import type { Database } from "~/lib/db/db";
import { cacheGet, cacheSet, cacheWrap } from "./cache-manager.server";

export interface QueryMetrics {
  queryCount: number;
  totalExecutionTime: number;
  avgExecutionTime: number;
  slowQueries: Array<{
    query: string;
    executionTime: number;
    timestamp: Date;
    params?: any;
  }>;
  cacheHits: number;
  cacheMisses: number;
  cacheHitRate: number;
}

export interface QueryOptions {
  cache?: boolean;
  cacheTTL?: number;
  cacheKey?: string;
  timeout?: number;
  retries?: number;
  logSlow?: boolean;
  slowThreshold?: number;
}

export interface ConnectionPoolMetrics {
  activeConnections: number;
  idleConnections: number;
  totalConnections: number;
  waitingRequests: number;
  connectionErrors: number;
  avgConnectionTime: number;
}

// Query performance metrics
const queryMetrics: QueryMetrics = {
  queryCount: 0,
  totalExecutionTime: 0,
  avgExecutionTime: 0,
  slowQueries: [],
  cacheHits: 0,
  cacheMisses: 0,
  cacheHitRate: 0,
};

// Connection pool simulation (in production, use actual connection pooling)
const connectionPoolMetrics: ConnectionPoolMetrics = {
  activeConnections: 0,
  idleConnections: 5,
  totalConnections: 5,
  waitingRequests: 0,
  connectionErrors: 0,
  avgConnectionTime: 0,
};

const MAX_SLOW_QUERIES = 100;
const DEFAULT_SLOW_THRESHOLD = 1000; // 1 second

/**
 * Execute query with optimization and monitoring
 */
export async function executeOptimizedQuery<T>(
  db: Database,
  queryFn: () => Promise<T>,
  options: QueryOptions = {}
): Promise<T> {
  const {
    cache = false,
    cacheTTL = 300,
    cacheKey,
    timeout = 30000,
    retries = 3,
    logSlow = true,
    slowThreshold = DEFAULT_SLOW_THRESHOLD,
  } = options;

  const startTime = Date.now();
  let result: T;
  const fromCache = false;

  try {
    // Try cache first if enabled
    if (cache && cacheKey) {
      const cached = cacheGet<T>(cacheKey);
      if (cached !== null) {
        queryMetrics.cacheHits++;
        updateQueryMetrics(0, true);
        return cached;
      }
      queryMetrics.cacheMisses++;
    }

    // Execute query with timeout and retries
    result = await executeWithRetries(queryFn, retries, timeout);

    // Cache result if enabled
    if (cache && cacheKey && result) {
      cacheSet(cacheKey, result, cacheTTL, "database");
    }
  } catch (error) {
    const executionTime = Date.now() - startTime;
    updateQueryMetrics(executionTime, fromCache);

    if (logSlow && executionTime > slowThreshold) {
      logSlowQuery("FAILED_QUERY", executionTime, error);
    }

    throw error;
  }

  const executionTime = Date.now() - startTime;
  updateQueryMetrics(executionTime, fromCache);

  // Log slow queries
  if (logSlow && executionTime > slowThreshold) {
    logSlowQuery("SLOW_QUERY", executionTime);
  }

  return result;
}

/**
 * Execute query with retries and timeout
 */
async function executeWithRetries<T>(
  queryFn: () => Promise<T>,
  retries: number,
  timeout: number
): Promise<T> {
  let lastError: Error | null = null;

  for (let attempt = 0; attempt <= retries; attempt++) {
    try {
      connectionPoolMetrics.activeConnections++;
      connectionPoolMetrics.idleConnections--;

      const result = await Promise.race([
        queryFn(),
        new Promise<never>((_, reject) =>
          setTimeout(() => reject(new Error("Query timeout")), timeout)
        ),
      ]);

      connectionPoolMetrics.activeConnections--;
      connectionPoolMetrics.idleConnections++;

      return result;
    } catch (error) {
      connectionPoolMetrics.activeConnections--;
      connectionPoolMetrics.idleConnections++;
      connectionPoolMetrics.connectionErrors++;

      lastError = error instanceof Error ? error : new Error(String(error));

      if (attempt < retries) {
        // Exponential backoff
        const delay = Math.min(1000 * Math.pow(2, attempt), 5000);
        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }
  }

  throw lastError || new Error("Query failed after retries");
}

/**
 * Cached database operations
 */
export const cachedQueries = {
  /**
   * Get user by UUID with caching
   */
  getUserByUuid: async (db: Database, userUuid: string) => {
    return executeOptimizedQuery(
      db,
      () =>
        db.query.users.findFirst({
          where: (users, { eq }) => eq(users.uuid, userUuid),
        }),
      {
        cache: true,
        cacheTTL: 600, // 10 minutes
        cacheKey: `user:${userUuid}`,
        slowThreshold: 500,
      }
    );
  },

  /**
   * Get user analytics with caching
   */
  getUserAnalytics: async (db: Database, userUuid: string, dateRange: string) => {
    return executeOptimizedQuery(
      db,
      () => {
        // This would be the actual analytics query
        return Promise.resolve({
          totalRequests: 0,
          totalCredits: 0,
          // ... other analytics data
        });
      },
      {
        cache: true,
        cacheTTL: 1800, // 30 minutes
        cacheKey: `analytics:${userUuid}:${dateRange}`,
        slowThreshold: 2000,
      }
    );
  },

  /**
   * Get system statistics with caching
   */
  getSystemStats: async (db: Database) => {
    return executeOptimizedQuery(
      db,
      async () => {
        // This would be actual system stats queries
        const [userCount, orderCount] = await Promise.all([
          db.query.users.findMany().then((users) => users.length),
          db.query.orders.findMany().then((orders) => orders.length),
        ]);

        return {
          totalUsers: userCount,
          totalOrders: orderCount,
          timestamp: new Date(),
        };
      },
      {
        cache: true,
        cacheTTL: 300, // 5 minutes
        cacheKey: "system:stats",
        slowThreshold: 1000,
      }
    );
  },

  /**
   * Get API usage statistics with caching
   */
  getApiUsageStats: async (db: Database, userUuid?: string) => {
    const cacheKey = userUuid ? `api-usage:${userUuid}` : "api-usage:global";

    return executeOptimizedQuery(
      db,
      () => {
        // This would be the actual API usage query
        return Promise.resolve({
          totalCalls: 0,
          successfulCalls: 0,
          failedCalls: 0,
          avgResponseTime: 0,
        });
      },
      {
        cache: true,
        cacheTTL: 180, // 3 minutes
        cacheKey,
        slowThreshold: 1500,
      }
    );
  },
};

/**
 * Batch query execution with optimization
 */
export async function executeBatchQueries<T extends Record<string, any>>(
  db: Database,
  queries: Record<keyof T, () => Promise<any>>,
  options: QueryOptions = {}
): Promise<T> {
  const startTime = Date.now();
  const results = {} as T;

  try {
    // Execute all queries in parallel
    const queryPromises = Object.entries(queries).map(async ([key, queryFn]) => {
      const result = await executeOptimizedQuery(db, queryFn as () => Promise<any>, {
        ...options,
        cacheKey: options.cacheKey ? `${options.cacheKey}:${key}` : undefined,
      });
      return [key, result];
    });

    const resolvedQueries = await Promise.all(queryPromises);

    for (const [key, result] of resolvedQueries) {
      results[key as keyof T] = result;
    }

    const executionTime = Date.now() - startTime;
    console.log(`[DB_OPTIMIZER] Batch query completed in ${executionTime}ms`);

    return results;
  } catch (error) {
    const executionTime = Date.now() - startTime;
    console.error(`[DB_OPTIMIZER] Batch query failed after ${executionTime}ms:`, error);
    throw error;
  }
}

/**
 * Query builder with automatic optimization
 */
export class OptimizedQueryBuilder {
  private db: Database;
  private options: QueryOptions;

  constructor(db: Database, options: QueryOptions = {}) {
    this.db = db;
    this.options = {
      cache: true,
      cacheTTL: 300,
      logSlow: true,
      slowThreshold: 1000,
      ...options,
    };
  }

  /**
   * Execute a select query with optimization
   */
  async select<T>(queryFn: () => Promise<T>, cacheKey?: string): Promise<T> {
    return executeOptimizedQuery(this.db, queryFn, {
      ...this.options,
      cacheKey,
    });
  }

  /**
   * Execute an insert query (no caching)
   */
  async insert<T>(queryFn: () => Promise<T>): Promise<T> {
    return executeOptimizedQuery(this.db, queryFn, {
      ...this.options,
      cache: false,
    });
  }

  /**
   * Execute an update query (invalidate cache)
   */
  async update<T>(queryFn: () => Promise<T>, invalidateKeys?: string[]): Promise<T> {
    const result = await executeOptimizedQuery(this.db, queryFn, {
      ...this.options,
      cache: false,
    });

    // Invalidate related cache entries
    if (invalidateKeys) {
      for (const key of invalidateKeys) {
        // This would invalidate cache entries matching the pattern
        console.log(`[DB_OPTIMIZER] Would invalidate cache key: ${key}`);
      }
    }

    return result;
  }

  /**
   * Execute a delete query (invalidate cache)
   */
  async delete<T>(queryFn: () => Promise<T>, invalidateKeys?: string[]): Promise<T> {
    return this.update(queryFn, invalidateKeys);
  }
}

/**
 * Update query metrics
 */
function updateQueryMetrics(executionTime: number, fromCache: boolean): void {
  if (!fromCache) {
    queryMetrics.queryCount++;
    queryMetrics.totalExecutionTime += executionTime;
    queryMetrics.avgExecutionTime = queryMetrics.totalExecutionTime / queryMetrics.queryCount;
  }

  const totalCacheRequests = queryMetrics.cacheHits + queryMetrics.cacheMisses;
  queryMetrics.cacheHitRate =
    totalCacheRequests > 0 ? (queryMetrics.cacheHits / totalCacheRequests) * 100 : 0;
}

/**
 * Log slow queries
 */
function logSlowQuery(type: string, executionTime: number, error?: any): void {
  const slowQuery = {
    query: type,
    executionTime,
    timestamp: new Date(),
    params: error ? { error: error.message } : undefined,
  };

  queryMetrics.slowQueries.push(slowQuery);

  // Keep only the most recent slow queries
  if (queryMetrics.slowQueries.length > MAX_SLOW_QUERIES) {
    queryMetrics.slowQueries = queryMetrics.slowQueries.slice(-MAX_SLOW_QUERIES);
  }

  console.warn(`[DB_OPTIMIZER] ${type}: ${executionTime}ms`, slowQuery);
}

/**
 * Get query performance metrics
 */
export function getQueryMetrics(): QueryMetrics {
  return { ...queryMetrics };
}

/**
 * Get connection pool metrics
 */
export function getConnectionPoolMetrics(): ConnectionPoolMetrics {
  return { ...connectionPoolMetrics };
}

/**
 * Reset query metrics
 */
export function resetQueryMetrics(): void {
  queryMetrics.queryCount = 0;
  queryMetrics.totalExecutionTime = 0;
  queryMetrics.avgExecutionTime = 0;
  queryMetrics.slowQueries = [];
  queryMetrics.cacheHits = 0;
  queryMetrics.cacheMisses = 0;
  queryMetrics.cacheHitRate = 0;
}

/**
 * Database health check
 */
export async function checkDatabaseHealth(db: Database): Promise<{
  healthy: boolean;
  responseTime: number;
  error?: string;
}> {
  const startTime = Date.now();

  try {
    // Simple health check query
    await executeOptimizedQuery(db, () => db.query.users.findFirst(), {
      cache: false,
      timeout: 5000,
    });

    const responseTime = Date.now() - startTime;
    return {
      healthy: true,
      responseTime,
    };
  } catch (error) {
    const responseTime = Date.now() - startTime;
    return {
      healthy: false,
      responseTime,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

/**
 * Optimize database indexes (placeholder for actual implementation)
 */
export async function optimizeIndexes(db: Database): Promise<{
  optimized: string[];
  recommendations: string[];
}> {
  // This would analyze query patterns and suggest/create indexes
  console.log("[DB_OPTIMIZER] Analyzing query patterns for index optimization...");

  return {
    optimized: [
      "users.uuid",
      "users.email",
      "orders.user_uuid",
      "api_usage.user_uuid",
      "api_usage.created_at",
    ],
    recommendations: [
      "Consider adding composite index on (user_uuid, created_at) for api_usage table",
      "Consider adding index on orders.status for faster status filtering",
      "Consider partitioning api_usage table by date for better performance",
    ],
  };
}

/**
 * Admin Deployment Dashboard
 * Comprehensive deployment management, CI/CD monitoring, and environment configuration
 */

import type { LoaderFunctionArgs } from "@remix-run/cloudflare";
import { json } from "@remix-run/cloudflare";
import { useLoaderData } from "@remix-run/react";
import {
  Activity,
  AlertTriangle,
  CheckCircle,
  Clock,
  Database,
  Download,
  Edit,
  ExternalLink,
  Eye,
  GitBranch,
  Globe,
  Info,
  Pause,
  Play,
  Plus,
  RefreshCw,
  Rocket,
  RotateCcw,
  Server,
  Settings,
  Shield,
  Trash2,
  Upload,
  XCircle,
  Zap,
} from "lucide-react";
import { useState } from "react";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";

// Mock data functions (in production, these would import from actual deployment modules)
const getDeploymentMetrics = () => ({
  totalDeployments: 156,
  successfulDeployments: 142,
  failedDeployments: 14,
  successRate: 91.0,
  averageDeploymentTime: 245000, // 4 minutes 5 seconds
  deploymentsToday: 8,
  deploymentsThisWeek: 34,
  recentDeployments: [
    {
      id: "deploy_1",
      version: "v2024.01.15.1430",
      environment: "production",
      status: "success",
      branch: "main",
      commit: "abc123",
      commitMessage: "feat: add new user dashboard",
      author: "john.doe",
      startTime: new Date(Date.now() - 300000), // 5 minutes ago
      endTime: new Date(Date.now() - 60000), // 1 minute ago
      duration: 240000,
      deploymentUrl: "https://app.example.com",
    },
    {
      id: "deploy_2",
      version: "v2024.01.15.1200",
      environment: "staging",
      status: "success",
      branch: "main",
      commit: "def456",
      commitMessage: "fix: resolve authentication issue",
      author: "jane.smith",
      startTime: new Date(Date.now() - 7200000), // 2 hours ago
      endTime: new Date(Date.now() - 7020000),
      duration: 180000,
      deploymentUrl: "https://staging.example.com",
    },
    {
      id: "deploy_3",
      version: "v2024.01.15.0900",
      environment: "development",
      status: "failed",
      branch: "develop",
      commit: "ghi789",
      commitMessage: "wip: experimental feature",
      author: "bob.wilson",
      startTime: new Date(Date.now() - 14400000), // 4 hours ago
      endTime: new Date(Date.now() - 14280000),
      duration: 120000,
    },
  ],
  deploymentsByEnvironment: { production: 45, staging: 67, development: 44 },
});

const getEnvironments = () => [
  {
    name: "production",
    type: "production",
    url: "https://app.example.com",
    branch: "main",
    autoDeployEnabled: false,
    requiresApproval: true,
    healthCheckUrl: "https://app.example.com/api/health",
    resources: { cpu: "2 vCPU", memory: "4 GB", storage: "100 GB" },
    scaling: { minInstances: 2, maxInstances: 10, targetCPU: 70 },
  },
  {
    name: "staging",
    type: "staging",
    url: "https://staging.example.com",
    branch: "main",
    autoDeployEnabled: true,
    requiresApproval: true,
    healthCheckUrl: "https://staging.example.com/api/health",
    resources: { cpu: "1 vCPU", memory: "1 GB", storage: "20 GB" },
    scaling: { minInstances: 1, maxInstances: 3, targetCPU: 70 },
  },
  {
    name: "development",
    type: "development",
    url: "https://dev.example.com",
    branch: "develop",
    autoDeployEnabled: true,
    requiresApproval: false,
    healthCheckUrl: "https://dev.example.com/api/health",
    resources: { cpu: "0.5 vCPU", memory: "512 MB", storage: "10 GB" },
    scaling: { minInstances: 1, maxInstances: 2, targetCPU: 70 },
  },
];

const getPipelines = () => [
  {
    id: "pipeline_1",
    name: "Production Pipeline",
    repository: "remix-cloudflare-neon-starter",
    branch: "main",
    environment: "production",
    status: "idle",
    enabled: true,
    lastRun: {
      id: "run_1",
      status: "success",
      startTime: new Date(Date.now() - 300000),
      duration: 240000,
      commit: "abc123",
      author: "john.doe",
    },
  },
  {
    id: "pipeline_2",
    name: "Staging Pipeline",
    repository: "remix-cloudflare-neon-starter",
    branch: "main",
    environment: "staging",
    status: "success",
    enabled: true,
    lastRun: {
      id: "run_2",
      status: "success",
      startTime: new Date(Date.now() - 7200000),
      duration: 180000,
      commit: "def456",
      author: "jane.smith",
    },
  },
  {
    id: "pipeline_3",
    name: "Development Pipeline",
    repository: "remix-cloudflare-neon-starter",
    branch: "develop",
    environment: "development",
    status: "failed",
    enabled: true,
    lastRun: {
      id: "run_3",
      status: "failed",
      startTime: new Date(Date.now() - 14400000),
      duration: 120000,
      commit: "ghi789",
      author: "bob.wilson",
    },
  },
];

const getBackups = () => [
  {
    id: "backup_1",
    type: "database",
    status: "completed",
    environment: "production",
    size: 524288000, // 500MB
    startTime: new Date(Date.now() - 86400000), // 1 day ago
    duration: 45000,
    location: "backups/production/database/2024-01-15",
  },
  {
    id: "backup_2",
    type: "full",
    status: "completed",
    environment: "staging",
    size: 1073741824, // 1GB
    startTime: new Date(Date.now() - 172800000), // 2 days ago
    duration: 120000,
    location: "backups/staging/full/2024-01-14",
  },
  {
    id: "backup_3",
    type: "files",
    status: "running",
    environment: "production",
    size: 0,
    startTime: new Date(Date.now() - 1800000), // 30 minutes ago
    location: "backups/production/files/2024-01-15",
  },
];

export async function loader({ request, context }: LoaderFunctionArgs) {
  try {
    // TODO: Add admin authentication check

    // Get deployment data
    const deploymentMetrics = getDeploymentMetrics();
    const environments = getEnvironments();
    const pipelines = getPipelines();
    const backups = getBackups();

    return json({
      success: true,
      data: {
        deploymentMetrics,
        environments,
        pipelines,
        backups,
        timestamp: new Date().toISOString(),
      },
    });
  } catch (error) {
    console.error("Error loading deployment dashboard:", error);
    return json({ success: false, error: "Failed to load deployment dashboard" }, { status: 500 });
  }
}

export default function AdminDeploymentPage() {
  const { data } = useLoaderData<typeof loader>();
  const [selectedTab, setSelectedTab] = useState("overview");

  const { deploymentMetrics, environments, pipelines, backups } = data;

  const getStatusColor = (status: string) => {
    switch (status) {
      case "success":
      case "completed":
      case "healthy":
        return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";
      case "failed":
      case "critical":
        return "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400";
      case "running":
      case "deploying":
      case "building":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400";
      case "pending":
      case "idle":
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
      default:
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "success":
      case "completed":
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case "failed":
        return <XCircle className="w-4 h-4 text-red-500" />;
      case "running":
      case "deploying":
      case "building":
        return <Activity className="w-4 h-4 text-blue-500 animate-spin" />;
      case "pending":
      case "idle":
        return <Clock className="w-4 h-4 text-gray-500" />;
      default:
        return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
    }
  };

  const formatDuration = (ms: number) => {
    const minutes = Math.floor(ms / 60000);
    const seconds = Math.floor((ms % 60000) / 1000);
    return `${minutes}m ${seconds}s`;
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                Deployment Management
              </h1>
              <p className="mt-2 text-gray-600 dark:text-gray-400">
                Manage deployments, CI/CD pipelines, and environment configurations
              </p>
            </div>
            <div className="flex items-center space-x-3">
              <Button variant="outline" size="sm">
                <Download className="w-4 h-4 mr-2" />
                Export Config
              </Button>
              <Button variant="outline" size="sm">
                <RefreshCw className="w-4 h-4 mr-2" />
                Refresh
              </Button>
              <Button size="sm">
                <Rocket className="w-4 h-4 mr-2" />
                Deploy Now
              </Button>
            </div>
          </div>
        </div>

        {/* Deployment Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Rocket className="w-8 h-8 text-blue-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Total Deployments
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {deploymentMetrics.totalDeployments}
                  </p>
                  <p className="text-xs text-blue-600">
                    {deploymentMetrics.deploymentsToday} today
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="w-8 h-8 text-green-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Success Rate
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {deploymentMetrics.successRate.toFixed(1)}%
                  </p>
                  <p className="text-xs text-green-600">
                    {deploymentMetrics.successfulDeployments} successful
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Clock className="w-8 h-8 text-purple-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Avg Duration
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {formatDuration(deploymentMetrics.averageDeploymentTime)}
                  </p>
                  <p className="text-xs text-purple-600">Per deployment</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Server className="w-8 h-8 text-orange-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Environments
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {environments.length}
                  </p>
                  <p className="text-xs text-orange-600">Active environments</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Detailed Deployment Information */}
        <Tabs value={selectedTab} onValueChange={setSelectedTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="deployments">Deployments</TabsTrigger>
            <TabsTrigger value="pipelines">Pipelines</TabsTrigger>
            <TabsTrigger value="environments">Environments</TabsTrigger>
            <TabsTrigger value="backups">Backups</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Recent Deployments */}
              <Card>
                <CardHeader>
                  <CardTitle>Recent Deployments</CardTitle>
                  <CardDescription>
                    Latest deployment activities across environments
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {deploymentMetrics.recentDeployments.map((deployment, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-3 border rounded-lg"
                      >
                        <div className="flex items-center space-x-3">
                          {getStatusIcon(deployment.status)}
                          <div>
                            <p className="font-medium text-sm">{deployment.version}</p>
                            <p className="text-xs text-gray-500">
                              {deployment.environment} • {deployment.author}
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <Badge className={getStatusColor(deployment.status)}>
                            {deployment.status}
                          </Badge>
                          <p className="text-xs text-gray-500 mt-1">
                            {deployment.duration
                              ? formatDuration(deployment.duration)
                              : "Running..."}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Environment Status */}
              <Card>
                <CardHeader>
                  <CardTitle>Environment Status</CardTitle>
                  <CardDescription>Current status of all environments</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {environments.map((env, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-3 border rounded-lg"
                      >
                        <div className="flex items-center space-x-3">
                          <Server className="w-5 h-5 text-blue-500" />
                          <div>
                            <p className="font-medium capitalize">{env.name}</p>
                            <p className="text-xs text-gray-500">
                              {env.resources.cpu} • {env.resources.memory}
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <Badge className={getStatusColor("healthy")}>Healthy</Badge>
                          <p className="text-xs text-gray-500 mt-1">
                            Auto-deploy: {env.autoDeployEnabled ? "On" : "Off"}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="deployments" className="space-y-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold">Deployment History</h3>
              <Button size="sm">
                <Rocket className="w-4 h-4 mr-2" />
                New Deployment
              </Button>
            </div>

            <Card>
              <CardContent className="p-0">
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gray-50 dark:bg-gray-800">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Version
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Environment
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Author
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Duration
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                      {deploymentMetrics.recentDeployments.map((deployment, index) => (
                        <tr key={index}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div>
                              <p className="text-sm font-medium text-gray-900 dark:text-white">
                                {deployment.version}
                              </p>
                              <p className="text-sm text-gray-500">
                                {deployment.commit.substring(0, 7)}
                              </p>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <Badge variant="outline" className="capitalize">
                              {deployment.environment}
                            </Badge>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center space-x-2">
                              {getStatusIcon(deployment.status)}
                              <Badge className={getStatusColor(deployment.status)}>
                                {deployment.status}
                              </Badge>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                            {deployment.author}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                            {deployment.duration ? formatDuration(deployment.duration) : "-"}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div className="flex items-center space-x-2">
                              <Button variant="outline" size="sm">
                                <Eye className="w-3 h-3" />
                              </Button>
                              {deployment.status === "success" && (
                                <Button variant="outline" size="sm">
                                  <RotateCcw className="w-3 h-3" />
                                </Button>
                              )}
                              {deployment.deploymentUrl && (
                                <Button variant="outline" size="sm" asChild>
                                  <a
                                    href={deployment.deploymentUrl}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                  >
                                    <ExternalLink className="w-3 h-3" />
                                  </a>
                                </Button>
                              )}
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="pipelines" className="space-y-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold">CI/CD Pipelines</h3>
              <Button size="sm">
                <Plus className="w-4 h-4 mr-2" />
                Create Pipeline
              </Button>
            </div>

            <div className="grid grid-cols-1 gap-6">
              {pipelines.map((pipeline, index) => (
                <Card key={index}>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <GitBranch className="w-5 h-5 text-blue-500" />
                        <div>
                          <CardTitle className="text-lg">{pipeline.name}</CardTitle>
                          <CardDescription>
                            {pipeline.repository} • {pipeline.branch} branch
                          </CardDescription>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge className={getStatusColor(pipeline.status)}>{pipeline.status}</Badge>
                        <Button variant="outline" size="sm">
                          <Play className="w-3 h-3" />
                        </Button>
                        <Button variant="outline" size="sm">
                          <Settings className="w-3 h-3" />
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                          Environment
                        </p>
                        <p className="text-sm text-gray-900 dark:text-white capitalize">
                          {pipeline.environment}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                          Last Run
                        </p>
                        <p className="text-sm text-gray-900 dark:text-white">
                          {pipeline.lastRun
                            ? new Date(pipeline.lastRun.startTime).toLocaleString()
                            : "Never"}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                          Duration
                        </p>
                        <p className="text-sm text-gray-900 dark:text-white">
                          {pipeline.lastRun?.duration
                            ? formatDuration(pipeline.lastRun.duration)
                            : "-"}
                        </p>
                      </div>
                    </div>

                    {pipeline.lastRun && (
                      <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            {getStatusIcon(pipeline.lastRun.status)}
                            <span className="text-sm font-medium">
                              Last run: {pipeline.lastRun.commit.substring(0, 7)}
                            </span>
                          </div>
                          <span className="text-sm text-gray-500">
                            by {pipeline.lastRun.author}
                          </span>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="environments" className="space-y-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold">Environment Configuration</h3>
              <Button size="sm">
                <Plus className="w-4 h-4 mr-2" />
                Add Environment
              </Button>
            </div>

            <div className="grid grid-cols-1 gap-6">
              {environments.map((env, index) => (
                <Card key={index}>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <Server className="w-5 h-5 text-blue-500" />
                        <div>
                          <CardTitle className="text-lg capitalize">{env.name}</CardTitle>
                          <CardDescription>
                            {env.url} • {env.branch} branch
                          </CardDescription>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge className={getStatusColor("healthy")}>Healthy</Badge>
                        <Button variant="outline" size="sm">
                          <Edit className="w-3 h-3" />
                        </Button>
                        <Button variant="outline" size="sm">
                          <ExternalLink className="w-3 h-3" />
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                      <div>
                        <p className="text-sm font-medium text-gray-600 dark:text-gray-400">CPU</p>
                        <p className="text-sm text-gray-900 dark:text-white">{env.resources.cpu}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                          Memory
                        </p>
                        <p className="text-sm text-gray-900 dark:text-white">
                          {env.resources.memory}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                          Storage
                        </p>
                        <p className="text-sm text-gray-900 dark:text-white">
                          {env.resources.storage}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                          Instances
                        </p>
                        <p className="text-sm text-gray-900 dark:text-white">
                          {env.scaling.minInstances}-{env.scaling.maxInstances}
                        </p>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="flex items-center space-x-2">
                        <div
                          className={`w-2 h-2 rounded-full ${env.autoDeployEnabled ? "bg-green-500" : "bg-gray-400"}`}
                        />
                        <span className="text-sm">Auto Deploy</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div
                          className={`w-2 h-2 rounded-full ${env.requiresApproval ? "bg-yellow-500" : "bg-gray-400"}`}
                        />
                        <span className="text-sm">Requires Approval</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 rounded-full bg-green-500" />
                        <span className="text-sm">Health Check</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="backups" className="space-y-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold">Backup Management</h3>
              <Button size="sm">
                <Database className="w-4 h-4 mr-2" />
                Create Backup
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <Database className="w-8 h-8 text-blue-500" />
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                        Database Backups
                      </p>
                      <p className="text-2xl font-bold text-gray-900 dark:text-white">
                        {backups.filter((b) => b.type === "database").length}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <Server className="w-8 h-8 text-green-500" />
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                        Full Backups
                      </p>
                      <p className="text-2xl font-bold text-gray-900 dark:text-white">
                        {backups.filter((b) => b.type === "full").length}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <Shield className="w-8 h-8 text-purple-500" />
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                        Total Size
                      </p>
                      <p className="text-2xl font-bold text-gray-900 dark:text-white">
                        {formatBytes(backups.reduce((sum, b) => sum + b.size, 0))}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardContent className="p-0">
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gray-50 dark:bg-gray-800">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Type
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Environment
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Size
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Created
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                      {backups.map((backup, index) => (
                        <tr key={index}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <Badge variant="outline" className="capitalize">
                              {backup.type}
                            </Badge>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <Badge variant="outline" className="capitalize">
                              {backup.environment}
                            </Badge>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center space-x-2">
                              {getStatusIcon(backup.status)}
                              <Badge className={getStatusColor(backup.status)}>
                                {backup.status}
                              </Badge>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                            {backup.size > 0 ? formatBytes(backup.size) : "-"}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                            {new Date(backup.startTime).toLocaleString()}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div className="flex items-center space-x-2">
                              <Button variant="outline" size="sm">
                                <Download className="w-3 h-3" />
                              </Button>
                              <Button variant="outline" size="sm">
                                <Upload className="w-3 h-3" />
                              </Button>
                              <Button variant="outline" size="sm">
                                <Trash2 className="w-3 h-3" />
                              </Button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
